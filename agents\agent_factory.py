"""
智能体工厂 (Agent Factory)

负责创建和管理所有智能体实例
"""

from typing import Dict, Any, Optional, List
import logging
import sys
import os

# 添加data目录到路径以导入AgentInteractionLogger
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data'))

try:
    from agent_interaction_logger import AgentInteractionLogger
except ImportError:
    AgentInteractionLogger = None

from .analyst_agents import NewsAnalystAgent, TechnicalAnalystAgent, FundamentalAnalystAgent
from .outlook_agents import BullishOutlookAgent, BearishOutlookAgent, NeutralObserverAgent
from .trader_agent import TraderAgent


class AgentFactory:
    """智能体工厂类"""
    
    def __init__(self, llm_interface=None, logger: Optional[logging.Logger] = None,
                 interaction_logger: Optional[AgentInteractionLogger] = None):
        """
        初始化智能体工厂

        参数:
            llm_interface: LLM接口实例
            logger: 日志记录器
            interaction_logger: 代理交互日志记录器
        """
        self.llm_interface = llm_interface
        self.logger = logger or self._create_default_logger()
        self.interaction_logger = interaction_logger
        
        # 智能体类映射
        self.agent_classes = {
            "NAA": NewsAnalystAgent,
            "TAA": TechnicalAnalystAgent,
            "FAA": FundamentalAnalystAgent,
            "BOA": BullishOutlookAgent,
            "BeOA": BearishOutlookAgent,
            "NOA": NeutralObserverAgent,
            "TRA": TraderAgent
        }
        
        self.logger.info("智能体工厂初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger("agents.AgentFactory")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def create_agent(self, agent_id: str):
        """
        创建单个智能体

        参数:
            agent_id: 智能体ID

        返回:
            智能体实例
        """
        if agent_id not in self.agent_classes:
            raise ValueError(f"未知的智能体ID: {agent_id}")

        agent_class = self.agent_classes[agent_id]
        agent = agent_class(
            llm_interface=self.llm_interface,
            logger=self.logger,
            interaction_logger=self.interaction_logger
        )

        self.logger.info(f"创建智能体: {agent_id}")
        return agent
    
    def create_all_agents(self) -> Dict[str, Any]:
        """
        创建所有智能体
        
        返回:
            智能体字典 {agent_id: agent_instance}
        """
        agents = {}
        
        for agent_id in self.agent_classes.keys():
            try:
                agents[agent_id] = self.create_agent(agent_id)
                self.logger.info(f"✅ 智能体 {agent_id} 创建成功")
            except Exception as e:
                self.logger.error(f"❌ 智能体 {agent_id} 创建失败: {e}")
        
        self.logger.info(f"创建完成，共 {len(agents)} 个智能体")
        return agents
    
    def create_agents_subset(self, agent_ids: List[str]) -> Dict[str, Any]:
        """
        创建指定的智能体子集
        
        参数:
            agent_ids: 要创建的智能体ID列表
            
        返回:
            智能体字典
        """
        agents = {}
        
        for agent_id in agent_ids:
            try:
                agents[agent_id] = self.create_agent(agent_id)
                self.logger.info(f"✅ 智能体 {agent_id} 创建成功")
            except Exception as e:
                self.logger.error(f"❌ 智能体 {agent_id} 创建失败: {e}")
        
        self.logger.info(f"创建完成，共 {len(agents)} 个智能体")
        return agents
    
    def get_available_agents(self) -> List[str]:
        """
        获取可用的智能体ID列表
        
        返回:
            智能体ID列表
        """
        return list(self.agent_classes.keys())
    
    def validate_agents(self, agents: Dict[str, Any]) -> Dict[str, bool]:
        """
        验证智能体是否正常工作
        
        参数:
            agents: 智能体字典
            
        返回:
            验证结果字典 {agent_id: is_valid}
        """
        validation_results = {}
        
        for agent_id, agent in agents.items():
            try:
                # 简单的验证：检查智能体是否有必要的方法
                if hasattr(agent, 'process') and hasattr(agent, 'get_prompt_template'):
                    validation_results[agent_id] = True
                    self.logger.info(f"✅ 智能体 {agent_id} 验证通过")
                else:
                    validation_results[agent_id] = False
                    self.logger.warning(f"⚠️ 智能体 {agent_id} 缺少必要方法")
            except Exception as e:
                validation_results[agent_id] = False
                self.logger.error(f"❌ 智能体 {agent_id} 验证失败: {e}")
        
        return validation_results