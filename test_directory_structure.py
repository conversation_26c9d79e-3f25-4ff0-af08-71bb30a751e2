#!/usr/bin/env python3
"""
测试目录结构创建
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_directory_structure():
    """测试目录结构创建"""
    logger = logging.getLogger("test_directory")
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    try:
        from data.agent_interaction_logger import AgentInteractionLogger
        
        # 测试不同的日期格式
        test_dates = [
            "2025-07-04",
            "2025-07-05", 
            datetime.now().strftime("%Y-%m-%d")
        ]
        
        test_agents = ["NAA", "TAA", "FAA", "TRA"]
        
        for test_date in test_dates:
            logger.info(f"测试日期: {test_date}")
            
            # 创建日志记录器
            interaction_logger = AgentInteractionLogger(
                base_path="test_structure/trading",
                enabled=True,
                logger=logger
            )
            
            # 设置实验日期
            interaction_logger.set_experiment_date(test_date)
            logger.info(f"设置实验日期: {test_date}")
            
            # 为每个代理创建测试记录
            for agent_name in test_agents:
                logger.info(f"  测试代理: {agent_name}")
                
                # 记录输入
                input_id = interaction_logger.log_agent_input(
                    agent_name=agent_name,
                    state_data={"test": "data"},
                    market_data={"price": 100.0},
                    previous_outputs={},
                    metadata={"test_date": test_date}
                )
                
                # 记录提示词
                prompt_id = interaction_logger.log_agent_prompt(
                    agent_name=agent_name,
                    prompt_template="测试提示词",
                    full_prompt="完整测试提示词",
                    prompt_version="1.0.0",
                    source="test"
                )
                
                # 记录输出
                output_id = interaction_logger.log_agent_output(
                    agent_name=agent_name,
                    input_id=input_id,
                    prompt_id=prompt_id,
                    raw_response="测试响应",
                    parsed_output={"result": "success"},
                    processing_time=0.5,
                    confidence=0.9,
                    reasoning="测试推理"
                )
                
                # 验证目录结构
                expected_dir = Path("test_structure/trading") / test_date / agent_name
                if expected_dir.exists():
                    logger.info(f"    ✅ 目录创建成功: {expected_dir}")
                    
                    # 检查文件
                    files = ["inputs.json", "prompts.json", "outputs.json"]
                    for file_name in files:
                        file_path = expected_dir / file_name
                        if file_path.exists():
                            logger.info(f"      ✅ 文件存在: {file_name}")
                        else:
                            logger.error(f"      ❌ 文件缺失: {file_name}")
                else:
                    logger.error(f"    ❌ 目录创建失败: {expected_dir}")
        
        # 显示最终的目录结构
        logger.info("\n最终目录结构:")
        base_path = Path("test_structure/trading")
        if base_path.exists():
            for item in sorted(base_path.rglob("*")):
                if item.is_file():
                    relative_path = item.relative_to(base_path)
                    logger.info(f"  📄 {relative_path}")
                elif item.is_dir() and item != base_path:
                    relative_path = item.relative_to(base_path)
                    logger.info(f"  📁 {relative_path}/")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False
    finally:
        # 清理测试数据
        try:
            import shutil
            if os.path.exists("test_structure"):
                shutil.rmtree("test_structure")
                logger.info("🧹 测试数据已清理")
        except Exception as e:
            logger.warning(f"清理失败: {e}")

if __name__ == "__main__":
    test_directory_structure()
