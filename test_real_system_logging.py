#!/usr/bin/env python3
"""
测试真实系统的日志记录行为
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_real_system_logging():
    """测试真实系统的日志记录"""
    logger = logging.getLogger("test_real_system")
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    try:
        from data.agent_interaction_logger import AgentInteractionLogger
        from agents.analyst_agents import NewsAnalystAgent
        from contribution_assessment.llm_interface import LLMInterface
        
        # 创建一个测试日期
        test_date = "2025-07-05"  # 使用不同的日期避免与现有数据冲突
        
        logger.info(f"测试真实系统日志记录 - 日期: {test_date}")
        
        # 创建日志记录器（模拟run_opro_system.py中的创建方式）
        interaction_logger = AgentInteractionLogger(
            base_path="data/trading",  # 使用真实路径
            enabled=True,
            logger=logger
        )
        
        # 设置实验日期
        interaction_logger.set_experiment_date(test_date)
        logger.info(f"设置实验日期: {test_date}")
        
        # 创建代理（模拟真实系统中的创建方式）
        agent = NewsAnalystAgent(
            llm_interface=None,  # 不使用真实LLM
            logger=logger,
            interaction_logger=interaction_logger
        )
        
        logger.info(f"创建代理: {agent.agent_id}")
        
        # 模拟真实的交易状态
        trading_state = {
            "current_date": test_date,
            "analysis_period": {
                "start_date": "2025-07-01",
                "end_date": test_date
            },
            "stock_data": {
                "AAPL": {
                    "open": 148.50,
                    "high": 152.00,
                    "low": 147.80,
                    "close": 150.25,
                    "volume": 2500000
                }
            },
            "portfolio": {"AAPL": 100},
            "cash": 50000.0,
            "previous_outputs": {}
        }
        
        # 运行代理处理
        logger.info("运行代理处理...")
        result = agent.call_llm(agent.get_prompt_template(), trading_state)
        
        logger.info(f"代理处理完成: {result.get('agent_id', 'N/A')}")
        
        # 验证目录结构
        expected_base_dir = Path("data/trading")
        expected_date_dir = expected_base_dir / test_date
        expected_agent_dir = expected_date_dir / agent.agent_id
        
        logger.info("验证目录结构:")
        logger.info(f"  基础目录: {expected_base_dir} - {'存在' if expected_base_dir.exists() else '不存在'}")
        logger.info(f"  日期目录: {expected_date_dir} - {'存在' if expected_date_dir.exists() else '不存在'}")
        logger.info(f"  代理目录: {expected_agent_dir} - {'存在' if expected_agent_dir.exists() else '不存在'}")
        
        if expected_agent_dir.exists():
            logger.info("  代理目录内容:")
            for item in expected_agent_dir.iterdir():
                if item.is_file():
                    file_size = item.stat().st_size
                    logger.info(f"    📄 {item.name} ({file_size} 字节)")
                elif item.is_dir():
                    logger.info(f"    📁 {item.name}/")
        
        # 获取日志摘要
        summary = interaction_logger.get_agent_log_summary(agent.agent_id)
        logger.info("日志摘要:")
        logger.info(f"  代理: {agent.agent_id}")
        logger.info(f"  输入记录: {summary.get('inputs_count', 0)}")
        logger.info(f"  提示词记录: {summary.get('prompts_count', 0)}")
        logger.info(f"  输出记录: {summary.get('outputs_count', 0)}")
        logger.info(f"  日志目录: {summary.get('log_directory', 'N/A')}")
        
        # 显示完整的目录结构
        logger.info("\n完整的data/trading目录结构:")
        if expected_base_dir.exists():
            for item in sorted(expected_base_dir.rglob("*")):
                if item.is_file():
                    relative_path = item.relative_to(expected_base_dir)
                    file_size = item.stat().st_size
                    logger.info(f"  📄 {relative_path} ({file_size} 字节)")
                elif item.is_dir() and item != expected_base_dir:
                    relative_path = item.relative_to(expected_base_dir)
                    logger.info(f"  📁 {relative_path}/")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def analyze_existing_structure():
    """分析现有的目录结构"""
    logger = logging.getLogger("analyze_structure")
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    logger.info("分析现有的data/trading目录结构:")
    
    base_dir = Path("data/trading")
    if not base_dir.exists():
        logger.warning("data/trading目录不存在")
        return
    
    # 按层级显示结构
    logger.info(f"📁 {base_dir}/")
    
    # 第一层：日期目录
    for date_dir in sorted(base_dir.iterdir()):
        if date_dir.is_dir():
            logger.info(f"  📁 {date_dir.name}/  (日期目录)")
            
            # 第二层：代理目录和文件
            for item in sorted(date_dir.iterdir()):
                if item.is_dir():
                    logger.info(f"    📁 {item.name}/  (代理目录)")
                    
                    # 第三层：代理文件
                    for agent_file in sorted(item.iterdir()):
                        if agent_file.is_file():
                            file_size = agent_file.stat().st_size
                            logger.info(f"      📄 {agent_file.name} ({file_size} 字节)")
                elif item.is_file():
                    file_size = item.stat().st_size
                    logger.info(f"    📄 {item.name} ({file_size} 字节) - 可能需要移动")
        elif date_dir.is_file():
            file_size = date_dir.stat().st_size
            logger.info(f"  📄 {date_dir.name} ({file_size} 字节) - 应该在日期目录中")

if __name__ == "__main__":
    print("=" * 60)
    print("分析现有结构")
    print("=" * 60)
    analyze_existing_structure()
    
    print("\n" + "=" * 60)
    print("测试真实系统日志记录")
    print("=" * 60)
    test_real_system_logging()
