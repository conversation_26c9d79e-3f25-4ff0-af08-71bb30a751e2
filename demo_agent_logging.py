#!/usr/bin/env python3
"""
代理日志记录功能演示脚本

展示如何使用新的代理交互日志记录功能
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_demo_logger():
    """设置演示日志记录器"""
    logger = logging.getLogger("demo_agent_logging")
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

def demo_basic_usage():
    """演示基本使用方法"""
    logger = setup_demo_logger()
    
    logger.info("🎯 演示1: 基本使用方法")
    logger.info("=" * 50)
    
    try:
        from data.agent_interaction_logger import AgentInteractionLogger
        
        # 创建日志记录器
        interaction_logger = AgentInteractionLogger(
            base_path="demo_logs/trading",
            enabled=True,
            logger=logger
        )
        
        # 设置实验日期
        experiment_date = "2025-07-04"
        interaction_logger.set_experiment_date(experiment_date)
        
        logger.info(f"✅ 创建日志记录器，实验日期: {experiment_date}")
        
        # 模拟代理交互过程
        agent_name = "NAA"
        
        # 1. 记录输入
        input_id = interaction_logger.log_agent_input(
            agent_name=agent_name,
            state_data={
                "current_date": "2025-07-04",
                "analysis_period": {"start_date": "2025-07-01", "end_date": "2025-07-04"}
            },
            market_data={
                "AAPL": {"price": 150.25, "volume": 2500000, "change": 2.5}
            },
            previous_outputs={},
            metadata={"session_id": "demo_session_001"}
        )
        logger.info(f"📥 记录代理输入: {input_id}")
        
        # 2. 记录提示词
        prompt_template = """你是一个专业的新闻分析师，专门分析市场新闻对股票价格的影响。
        
你的任务是：
1. 分析当前的市场新闻和舆论情绪
2. 评估新闻对目标股票的影响程度
3. 量化市场情绪（-1到1，-1极度悲观，1极度乐观）
4. 提供新闻摘要和关键事件"""
        
        full_prompt = f"{prompt_template}\n\n当前日期: 2025-07-04\n股票: AAPL\n价格: $150.25"
        
        prompt_id = interaction_logger.log_agent_prompt(
            agent_name=agent_name,
            prompt_template=prompt_template,
            full_prompt=full_prompt,
            prompt_version="1.0.0",
            source="default",
            metadata={"prompt_length": len(full_prompt)}
        )
        logger.info(f"📝 记录代理提示词: {prompt_id}")
        
        # 3. 记录输出
        output_data = {
            "sentiment": 0.3,
            "summary": "苹果公司发布新产品，市场反应积极",
            "key_events": ["新产品发布", "季度财报超预期"],
            "confidence": 0.85,
            "reasoning": "基于最新新闻分析，市场情绪偏向乐观"
        }
        
        output_id = interaction_logger.log_agent_output(
            agent_name=agent_name,
            input_id=input_id,
            prompt_id=prompt_id,
            raw_response=json.dumps(output_data, ensure_ascii=False),
            parsed_output=output_data,
            processing_time=2.3,
            llm_used=True,
            confidence=0.85,
            reasoning=output_data["reasoning"],
            metadata={"model": "glm-4-flash", "tokens_used": 1250}
        )
        logger.info(f"📤 记录代理输出: {output_id}")
        
        # 获取日志摘要
        summary = interaction_logger.get_agent_log_summary(agent_name)
        logger.info(f"📊 日志摘要:")
        logger.info(f"   代理: {agent_name}")
        logger.info(f"   输入记录: {summary.get('inputs_count', 0)}")
        logger.info(f"   提示词记录: {summary.get('prompts_count', 0)}")
        logger.info(f"   输出记录: {summary.get('outputs_count', 0)}")
        logger.info(f"   日志目录: {summary.get('log_directory', 'N/A')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 基本使用演示失败: {e}")
        return False

def demo_agent_integration():
    """演示与代理系统的集成"""
    logger = setup_demo_logger()
    
    logger.info("\n🤖 演示2: 代理系统集成")
    logger.info("=" * 50)
    
    try:
        from data.agent_interaction_logger import AgentInteractionLogger
        from agents.analyst_agents import NewsAnalystAgent, TechnicalAnalystAgent
        
        # 创建日志记录器
        interaction_logger = AgentInteractionLogger(
            base_path="demo_logs/integration",
            enabled=True,
            logger=logger
        )
        
        # 创建多个代理
        agents = {
            "NAA": NewsAnalystAgent(
                llm_interface=None,  # 演示模式，不使用真实LLM
                logger=logger,
                interaction_logger=interaction_logger
            ),
            "TAA": TechnicalAnalystAgent(
                llm_interface=None,
                logger=logger,
                interaction_logger=interaction_logger
            )
        }
        
        logger.info(f"✅ 创建代理: {list(agents.keys())}")
        
        # 模拟交易状态
        trading_state = {
            "current_date": "2025-07-04",
            "stock_data": {
                "AAPL": {
                    "open": 148.50,
                    "high": 152.00,
                    "low": 147.80,
                    "close": 150.25,
                    "volume": 2500000
                }
            },
            "portfolio": {"AAPL": 100},
            "cash": 50000.0,
            "previous_outputs": {}
        }
        
        # 依次运行代理
        results = {}
        for agent_id, agent in agents.items():
            logger.info(f"🔄 运行代理: {agent_id}")
            
            # 代理处理会自动记录日志
            result = agent.call_llm(
                agent.get_prompt_template(),
                trading_state
            )
            
            results[agent_id] = result
            logger.info(f"✅ {agent_id} 处理完成")
            
            # 将结果添加到状态中，供下一个代理使用
            trading_state["previous_outputs"][agent_id] = result
        
        # 获取所有代理的日志摘要
        all_summary = interaction_logger.get_all_agents_summary()
        logger.info(f"📊 所有代理日志摘要:")
        logger.info(f"   实验日期: {all_summary.get('experiment_date', 'N/A')}")
        logger.info(f"   代理总数: {all_summary.get('total_agents', 0)}")
        
        for agent_name, agent_summary in all_summary.get("agents", {}).items():
            logger.info(f"   {agent_name}:")
            logger.info(f"     输入: {agent_summary.get('inputs_count', 0)}")
            logger.info(f"     提示词: {agent_summary.get('prompts_count', 0)}")
            logger.info(f"     输出: {agent_summary.get('outputs_count', 0)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 代理集成演示失败: {e}")
        return False

def demo_export_functionality():
    """演示导出功能"""
    logger = setup_demo_logger()
    
    logger.info("\n📁 演示3: 导出功能")
    logger.info("=" * 50)
    
    try:
        from data.agent_interaction_logger import AgentInteractionLogger
        
        # 使用之前创建的日志数据
        interaction_logger = AgentInteractionLogger(
            base_path="demo_logs/trading",
            enabled=True,
            logger=logger
        )
        
        # 导出代理日志
        export_result = interaction_logger.export_agent_logs("NAA", "json")
        
        if export_result.get("success", False):
            logger.info(f"✅ 导出成功:")
            logger.info(f"   文件路径: {export_result['export_path']}")
            logger.info(f"   导出格式: {export_result['export_format']}")
            logger.info(f"   记录统计:")
            for record_type, count in export_result.get("records_exported", {}).items():
                logger.info(f"     {record_type}: {count}")
            
            # 验证导出文件
            export_path = Path(export_result['export_path'])
            if export_path.exists():
                file_size = export_path.stat().st_size
                logger.info(f"   文件大小: {file_size} 字节")
                
                # 读取并显示部分内容
                with open(export_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    logger.info(f"   导出数据包含:")
                    for key in data.keys():
                        if isinstance(data[key], list):
                            logger.info(f"     {key}: {len(data[key])} 条记录")
                        else:
                            logger.info(f"     {key}: {data[key]}")
            
            return True
        else:
            logger.error(f"❌ 导出失败: {export_result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 导出功能演示失败: {e}")
        return False

def cleanup_demo_data():
    """清理演示数据"""
    logger = setup_demo_logger()
    
    try:
        import shutil
        
        demo_dirs = ["demo_logs"]
        
        for demo_dir in demo_dirs:
            if os.path.exists(demo_dir):
                shutil.rmtree(demo_dir)
                logger.info(f"🧹 清理演示目录: {demo_dir}")
        
    except Exception as e:
        logger.warning(f"⚠️ 清理演示数据失败: {e}")

def main():
    """主演示函数"""
    logger = setup_demo_logger()
    
    logger.info("🎬 代理交互日志记录功能演示")
    logger.info(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 60)
    
    demos = [
        ("基本使用方法", demo_basic_usage),
        ("代理系统集成", demo_agent_integration),
        ("导出功能", demo_export_functionality)
    ]
    
    results = []
    for demo_name, demo_func in demos:
        try:
            result = demo_func()
            results.append(result)
            if result:
                logger.info(f"✅ {demo_name} 演示成功")
            else:
                logger.error(f"❌ {demo_name} 演示失败")
        except Exception as e:
            logger.error(f"❌ {demo_name} 演示异常: {e}")
            results.append(False)
    
    # 总结
    passed = sum(results)
    total = len(results)
    
    logger.info("\n" + "=" * 60)
    logger.info("演示总结")
    logger.info("=" * 60)
    logger.info(f"成功演示: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 所有演示成功！代理日志记录功能已准备就绪。")
        
        logger.info("\n📖 使用指南:")
        logger.info("1. 启用日志记录: --enable-agent-logging")
        logger.info("2. 禁用日志记录: --disable-agent-logging")
        logger.info("3. 自定义路径: --agent-log-path 'custom/path'")
        logger.info("4. 设置日期: --agent-log-date '2025-07-04'")
        logger.info("5. 查看摘要: --agent-log-summary")
        logger.info("6. 导出日志: --export-agent-logs")
        
        logger.info("\n📁 日志文件结构:")
        logger.info("/data/trading/{experiment_date}/{agent_name}/")
        logger.info("├── inputs.json     - 代理输入数据")
        logger.info("├── prompts.json    - 代理提示词数据")
        logger.info("└── outputs.json    - 代理输出数据")
        
    else:
        logger.warning(f"⚠️ {total - passed} 个演示失败")
    
    # 清理演示数据
    cleanup_demo_data()
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
