#!/usr/bin/env python3
"""
代理日志记录功能测试脚本

测试新实现的代理交互日志记录功能，验证：
1. 代理输入、提示词、输出的记录
2. 文件结构的正确性
3. 命令行参数的功能
4. 日志摘要和导出功能

作者: AI Assistant
创建时间: 2025-07-04
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_test_logger():
    """设置测试日志记录器"""
    logger = logging.getLogger("test_agent_logging")
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

def test_agent_interaction_logger():
    """测试代理交互日志记录器的基本功能"""
    logger = setup_test_logger()
    logger.info("=" * 60)
    logger.info("测试代理交互日志记录器")
    logger.info("=" * 60)
    
    try:
        from data.agent_interaction_logger import AgentInteractionLogger
        
        # 创建测试日志记录器
        test_path = "test_data/agent_logs"
        interaction_logger = AgentInteractionLogger(
            base_path=test_path,
            enabled=True,
            logger=logger
        )
        
        # 设置测试日期
        test_date = "2025-07-04"
        interaction_logger.set_experiment_date(test_date)
        
        logger.info(f"✅ 代理交互日志记录器创建成功")
        logger.info(f"   基础路径: {test_path}")
        logger.info(f"   实验日期: {test_date}")
        
        # 测试记录代理输入
        input_id = interaction_logger.log_agent_input(
            agent_name="NAA",
            state_data={"current_date": "2025-07-04", "stock": "AAPL"},
            market_data={"price": 150.0, "volume": 1000000},
            previous_outputs={},
            metadata={"test": True}
        )
        
        logger.info(f"✅ 代理输入记录成功: {input_id}")
        
        # 测试记录代理提示词
        prompt_id = interaction_logger.log_agent_prompt(
            agent_name="NAA",
            prompt_template="你是一个新闻分析师...",
            full_prompt="你是一个新闻分析师... 当前日期: 2025-07-04",
            prompt_version="1.0.0",
            source="default",
            metadata={"test": True}
        )
        
        logger.info(f"✅ 代理提示词记录成功: {prompt_id}")
        
        # 测试记录代理输出
        output_id = interaction_logger.log_agent_output(
            agent_name="NAA",
            input_id=input_id,
            prompt_id=prompt_id,
            raw_response="测试响应",
            parsed_output={"sentiment": 0.5, "confidence": 0.8},
            processing_time=1.5,
            llm_used=True,
            confidence=0.8,
            reasoning="基于新闻分析的测试推理",
            metadata={"test": True}
        )
        
        logger.info(f"✅ 代理输出记录成功: {output_id}")
        
        # 测试获取日志摘要
        summary = interaction_logger.get_agent_log_summary("NAA")
        logger.info(f"✅ 代理日志摘要获取成功:")
        logger.info(f"   输入记录数: {summary.get('inputs_count', 0)}")
        logger.info(f"   提示词记录数: {summary.get('prompts_count', 0)}")
        logger.info(f"   输出记录数: {summary.get('outputs_count', 0)}")
        
        # 测试导出功能
        export_result = interaction_logger.export_agent_logs("NAA", "json")
        if export_result.get("success", False):
            logger.info(f"✅ 代理日志导出成功: {export_result['export_path']}")
        else:
            logger.error(f"❌ 代理日志导出失败: {export_result.get('error', '未知错误')}")
        
        # 验证文件结构
        agent_dir = Path(test_path) / test_date / "NAA"
        expected_files = ["inputs.json", "prompts.json", "outputs.json"]
        
        logger.info("验证文件结构:")
        for file_name in expected_files:
            file_path = agent_dir / file_name
            if file_path.exists():
                logger.info(f"   ✅ {file_name} 存在")
                
                # 验证文件内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    logger.info(f"      记录数: {len(data)}")
            else:
                logger.error(f"   ❌ {file_name} 不存在")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 代理交互日志记录器测试失败: {e}")
        return False

def test_base_agent_integration():
    """测试基础代理类的集成"""
    logger = setup_test_logger()
    logger.info("=" * 60)
    logger.info("测试基础代理类集成")
    logger.info("=" * 60)
    
    try:
        from data.agent_interaction_logger import AgentInteractionLogger
        from agents.analyst_agents import NewsAnalystAgent
        
        # 创建交互日志记录器
        interaction_logger = AgentInteractionLogger(
            base_path="test_data/agent_integration",
            enabled=True,
            logger=logger
        )
        
        # 创建测试代理
        agent = NewsAnalystAgent(
            llm_interface=None,  # 不使用真实LLM
            logger=logger,
            interaction_logger=interaction_logger
        )
        
        logger.info(f"✅ 代理创建成功: {agent.agent_id}")
        
        # 测试代理处理（会触发日志记录）
        test_state = {
            "current_date": "2025-07-04",
            "stock_data": {"AAPL": {"close": 150.0}},
            "previous_outputs": {}
        }
        
        # 由于没有LLM接口，会使用默认输出
        result = agent.call_llm("测试提示词", test_state)
        
        logger.info(f"✅ 代理处理完成")
        logger.info(f"   结果: {result.get('analysis', 'N/A')}")
        
        # 验证日志记录
        summary = interaction_logger.get_agent_log_summary(agent.agent_id)
        logger.info(f"✅ 日志记录验证:")
        logger.info(f"   输入记录数: {summary.get('inputs_count', 0)}")
        logger.info(f"   提示词记录数: {summary.get('prompts_count', 0)}")
        logger.info(f"   输出记录数: {summary.get('outputs_count', 0)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 基础代理类集成测试失败: {e}")
        return False

def test_command_line_interface():
    """测试命令行接口"""
    logger = setup_test_logger()
    logger.info("=" * 60)
    logger.info("测试命令行接口")
    logger.info("=" * 60)
    
    try:
        import subprocess
        
        # 测试帮助信息
        result = subprocess.run([
            sys.executable, "run_opro_system.py", "--help"
        ], capture_output=True, text=True, timeout=30)
        
        if "enable-agent-logging" in result.stdout:
            logger.info("✅ 代理日志记录参数在帮助信息中找到")
        else:
            logger.warning("⚠️ 代理日志记录参数未在帮助信息中找到")
        
        # 测试快速运行（禁用代理日志记录）
        logger.info("测试快速运行（禁用代理日志记录）...")
        result = subprocess.run([
            sys.executable, "run_opro_system.py",
            "--provider", "zhipuai",
            "--mode", "evaluation",
            "--quick-test",
            "--disable-agent-logging",
            "--disable-opro"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            logger.info("✅ 快速运行测试成功")
        else:
            logger.warning(f"⚠️ 快速运行测试返回码: {result.returncode}")
            if result.stderr:
                logger.warning(f"错误输出: {result.stderr[:200]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 命令行接口测试失败: {e}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    logger = setup_test_logger()
    
    try:
        import shutil
        
        test_dirs = ["test_data"]
        
        for test_dir in test_dirs:
            if os.path.exists(test_dir):
                shutil.rmtree(test_dir)
                logger.info(f"✅ 清理测试目录: {test_dir}")
        
    except Exception as e:
        logger.warning(f"⚠️ 清理测试数据失败: {e}")

def main():
    """主测试函数"""
    logger = setup_test_logger()
    
    logger.info("🧪 开始代理日志记录功能测试")
    logger.info(f"测试时间: {datetime.now().isoformat()}")
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("代理交互日志记录器基本功能", test_agent_interaction_logger),
        ("基础代理类集成", test_base_agent_integration),
        ("命令行接口", test_command_line_interface)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            test_results.append((test_name, result))
            if result:
                logger.info(f"✅ 测试通过: {test_name}")
            else:
                logger.error(f"❌ 测试失败: {test_name}")
        except Exception as e:
            logger.error(f"❌ 测试异常: {test_name} - {e}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    logger.info("\n" + "=" * 60)
    logger.info("测试总结")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{status}: {test_name}")
    
    logger.info(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！代理日志记录功能正常工作。")
    else:
        logger.warning(f"⚠️ {total - passed} 个测试失败，请检查相关功能。")
    
    # 清理测试数据
    cleanup_test_data()
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
