"""
分析层智能体 (Analyst Agents)

包含新闻分析、技术分析和基本面分析智能体的具体实现
"""

from typing import Dict, Any
from .base_agent import BaseAgent


class NewsAnalystAgent(BaseAgent):
    """新闻分析智能体 (NAA)"""
    
    def __init__(self, llm_interface=None, logger=None, interaction_logger=None):
        super().__init__("NAA", llm_interface, logger, interaction_logger)
    
    def get_prompt_template(self) -> str:
        return """你是一个专业的新闻分析师，专门分析市场新闻对股票价格的影响。

你的任务是：
1. 分析当前的市场新闻和舆论情绪
2. 评估新闻对目标股票的影响程度
3. 量化市场情绪（-1到1，-1极度悲观，1极度乐观）
4. 提供新闻摘要和关键事件

请返回JSON格式的分析结果，包含：
- sentiment: 情绪评分（-1到1）
- summary: 新闻摘要
- key_events: 关键事件列表
- impact_assessment: 影响评估
- confidence: 分析信心度（0到1）"""
    
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """处理新闻分析"""
        prompt = self.get_prompt_template()
        return self.call_llm(prompt, state)


class TechnicalAnalystAgent(BaseAgent):
    """技术分析智能体 (TAA)"""
    
    def __init__(self, llm_interface=None, logger=None, interaction_logger=None):
        super().__init__("TAA", llm_interface, logger, interaction_logger)
    
    def get_prompt_template(self) -> str:
        return """你是一个专业的技术分析师，专门通过图表模式和技术指标分析股票趋势。

你的任务是：
1. 分析股票的价格趋势和动量
2. 识别关键的支撑位和阻力位
3. 评估技术指标信号（RSI、MACD、移动平均线等）
4. 判断当前技术面是看涨、看跌还是中性

请返回JSON格式的分析结果，包含：
- trend: 趋势方向（bullish/bearish/neutral）
- support_level: 支撑位价格
- resistance_level: 阻力位价格
- technical_score: 技术评分（-1到1）
- indicators: 关键技术指标分析
- confidence: 分析信心度（0到1）"""
    
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """处理技术分析"""
        prompt = self.get_prompt_template()
        return self.call_llm(prompt, state)


class FundamentalAnalystAgent(BaseAgent):
    """基本面分析智能体 (FAA)"""
    
    def __init__(self, llm_interface=None, logger=None, interaction_logger=None):
        super().__init__("FAA", llm_interface, logger, interaction_logger)
    
    def get_prompt_template(self) -> str:
        return """你是一个专业的基本面分析师，专门分析公司的财务健康状况和内在价值。

你的任务是：
1. 评估公司的财务健康状况
2. 分析行业地位和竞争优势
3. 评估当前估值水平（高估/低估/合理）
4. 分析长期投资价值

请返回JSON格式的分析结果，包含：
- valuation: 估值评估（overvalued/undervalued/fair）
- financial_health: 财务健康评分（0到10）
- competitive_position: 竞争地位评估
- long_term_outlook: 长期前景
- intrinsic_value_estimate: 内在价值估计
- confidence: 分析信心度（0到1）"""
    
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """处理基本面分析"""
        prompt = self.get_prompt_template()
        return self.call_llm(prompt, state)