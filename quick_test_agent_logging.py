#!/usr/bin/env python3
"""
代理日志记录功能快速测试

验证核心功能是否正常工作
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logger():
    """设置日志记录器"""
    logger = logging.getLogger("quick_test")
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

def test_basic_functionality():
    """测试基本功能"""
    logger = setup_logger()
    
    try:
        from data.agent_interaction_logger import AgentInteractionLogger
        
        # 创建测试日志记录器
        interaction_logger = AgentInteractionLogger(
            base_path="quick_test_logs",
            enabled=True,
            logger=logger
        )
        
        # 记录测试数据
        input_id = interaction_logger.log_agent_input(
            agent_name="TEST_AGENT",
            state_data={"test": "data"},
            market_data={"price": 100.0},
            previous_outputs={},
            metadata={"test": True}
        )
        
        prompt_id = interaction_logger.log_agent_prompt(
            agent_name="TEST_AGENT",
            prompt_template="测试提示词",
            full_prompt="完整测试提示词",
            prompt_version="1.0.0",
            source="test"
        )
        
        output_id = interaction_logger.log_agent_output(
            agent_name="TEST_AGENT",
            input_id=input_id,
            prompt_id=prompt_id,
            raw_response="测试响应",
            parsed_output={"result": "success"},
            processing_time=0.5,
            confidence=0.9,
            reasoning="测试推理"
        )
        
        # 验证文件创建
        test_date = datetime.now().strftime("%Y-%m-%d")
        agent_dir = Path("quick_test_logs") / test_date / "TEST_AGENT"
        
        files_created = []
        for file_name in ["inputs.json", "prompts.json", "outputs.json"]:
            file_path = agent_dir / file_name
            if file_path.exists():
                files_created.append(file_name)
                
                # 验证文件内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if len(data) > 0:
                        logger.info(f"✅ {file_name}: {len(data)} 条记录")
                    else:
                        logger.warning(f"⚠️ {file_name}: 无记录")
            else:
                logger.error(f"❌ {file_name}: 文件未创建")
        
        # 测试摘要功能
        summary = interaction_logger.get_agent_log_summary("TEST_AGENT")
        logger.info(f"📊 日志摘要:")
        logger.info(f"   输入: {summary.get('inputs_count', 0)}")
        logger.info(f"   提示词: {summary.get('prompts_count', 0)}")
        logger.info(f"   输出: {summary.get('outputs_count', 0)}")
        
        if len(files_created) == 3:
            logger.info("🎉 基本功能测试通过！")
            return True
        else:
            logger.error(f"❌ 只创建了 {len(files_created)}/3 个文件")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理测试数据
        try:
            import shutil
            if os.path.exists("quick_test_logs"):
                shutil.rmtree("quick_test_logs")
                logger.info("🧹 测试数据已清理")
        except Exception as e:
            logger.warning(f"⚠️ 清理失败: {e}")

def test_agent_integration():
    """测试代理集成"""
    logger = setup_logger()
    
    try:
        from data.agent_interaction_logger import AgentInteractionLogger
        from agents.analyst_agents import NewsAnalystAgent
        
        # 创建日志记录器
        interaction_logger = AgentInteractionLogger(
            base_path="agent_integration_test",
            enabled=True,
            logger=logger
        )
        
        # 创建代理
        agent = NewsAnalystAgent(
            llm_interface=None,  # 使用模拟模式
            logger=logger,
            interaction_logger=interaction_logger
        )
        
        # 测试代理处理
        test_state = {
            "current_date": "2025-07-04",
            "stock_data": {"AAPL": {"close": 150.0}},
            "previous_outputs": {}
        }
        
        result = agent.call_llm("测试提示词", test_state)
        
        # 验证结果
        if result and "agent_id" in result:
            logger.info(f"✅ 代理处理成功: {result['agent_id']}")
            
            # 检查日志记录
            summary = interaction_logger.get_agent_log_summary(agent.agent_id)
            total_records = (summary.get('inputs_count', 0) + 
                           summary.get('prompts_count', 0) + 
                           summary.get('outputs_count', 0))
            
            if total_records > 0:
                logger.info(f"✅ 日志记录成功: {total_records} 条记录")
                return True
            else:
                logger.error("❌ 未找到日志记录")
                return False
        else:
            logger.error("❌ 代理处理失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 代理集成测试失败: {e}")
        return False
    finally:
        # 清理测试数据
        try:
            import shutil
            if os.path.exists("agent_integration_test"):
                shutil.rmtree("agent_integration_test")
                logger.info("🧹 测试数据已清理")
        except Exception as e:
            logger.warning(f"⚠️ 清理失败: {e}")

def main():
    """主测试函数"""
    logger = setup_logger()
    
    logger.info("🚀 开始代理日志记录功能快速测试")
    
    tests = [
        ("基本功能", test_basic_functionality),
        ("代理集成", test_agent_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n🔍 测试: {test_name}")
        try:
            result = test_func()
            results.append(result)
            if result:
                logger.info(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
            results.append(False)
    
    # 总结
    passed = sum(results)
    total = len(results)
    
    logger.info(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！代理日志记录功能正常工作。")
        return True
    else:
        logger.warning(f"⚠️ {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
