#!/usr/bin/env python3
"""
测试基于交易日期的分类功能

验证代理日志记录器能够根据交易日期（2025-01-01 到 2025-04-01）
正确创建分类文件夹结构
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logger():
    """设置日志记录器"""
    logger = logging.getLogger("test_trading_date")
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

def generate_test_dates():
    """生成测试日期范围（2025-01-01 到 2025-04-01）"""
    start_date = datetime(2025, 1, 1)
    end_date = datetime(2025, 4, 1)
    
    dates = []
    current_date = start_date
    
    # 生成一些代表性的日期
    while current_date <= end_date:
        dates.append(current_date.strftime("%Y-%m-%d"))
        current_date += timedelta(days=7)  # 每周一个日期
    
    return dates

def test_trading_date_extraction():
    """测试交易日期提取功能"""
    logger = setup_logger()
    logger.info("🔍 测试交易日期提取功能")
    
    try:
        from data.agent_interaction_logger import AgentInteractionLogger
        
        # 创建日志记录器
        interaction_logger = AgentInteractionLogger(
            base_path="test_trading_dates/extraction",
            enabled=True,
            logger=logger,
            use_trading_dates=True
        )
        
        # 测试不同的状态数据格式
        test_cases = [
            {
                "name": "current_date字段",
                "state_data": {"current_date": "2025-01-15"},
                "expected": "2025-01-15"
            },
            {
                "name": "analysis_period字段",
                "state_data": {
                    "analysis_period": {
                        "start_date": "2025-02-01",
                        "end_date": "2025-02-15"
                    }
                },
                "expected": "2025-02-15"
            },
            {
                "name": "trading_date字段",
                "state_data": {"trading_date": "2025-03-10"},
                "expected": "2025-03-10"
            },
            {
                "name": "无日期字段",
                "state_data": {"stock_data": {"AAPL": {"price": 150.0}}},
                "expected": None
            }
        ]
        
        for test_case in test_cases:
            logger.info(f"  测试: {test_case['name']}")
            
            extracted_date = interaction_logger._extract_trading_date(test_case["state_data"])
            
            if extracted_date == test_case["expected"]:
                logger.info(f"    ✅ 提取成功: {extracted_date}")
            else:
                logger.error(f"    ❌ 提取失败: 期望 {test_case['expected']}, 得到 {extracted_date}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 交易日期提取测试失败: {e}")
        return False

def test_date_based_classification():
    """测试基于日期的分类功能"""
    logger = setup_logger()
    logger.info("📁 测试基于日期的分类功能")
    
    try:
        from data.agent_interaction_logger import AgentInteractionLogger
        from agents.analyst_agents import NewsAnalystAgent
        
        # 创建日志记录器
        interaction_logger = AgentInteractionLogger(
            base_path="test_trading_dates/classification",
            enabled=True,
            logger=logger,
            use_trading_dates=True
        )
        
        # 生成测试日期
        test_dates = generate_test_dates()
        test_agents = ["NAA", "TAA", "FAA"]
        
        logger.info(f"测试日期范围: {test_dates[0]} 到 {test_dates[-1]} ({len(test_dates)} 个日期)")
        logger.info(f"测试代理: {test_agents}")
        
        # 为每个日期和代理创建测试记录
        for i, test_date in enumerate(test_dates[:5]):  # 只测试前5个日期
            logger.info(f"  处理日期: {test_date}")
            
            for agent_name in test_agents:
                # 创建包含交易日期的状态数据
                state_data = {
                    "current_date": test_date,
                    "analysis_period": {
                        "start_date": test_date,
                        "end_date": test_date
                    },
                    "stock_data": {
                        "AAPL": {"price": 150.0 + i, "volume": 1000000}
                    },
                    "portfolio": {"AAPL": 100},
                    "cash": 50000.0,
                    "previous_outputs": {}
                }
                
                # 记录代理输入
                input_id = interaction_logger.log_agent_input(
                    agent_name=agent_name,
                    state_data=state_data,
                    market_data=state_data["stock_data"],
                    previous_outputs={},
                    metadata={"test_date": test_date}
                )
                
                # 记录代理提示词
                prompt_id = interaction_logger.log_agent_prompt(
                    agent_name=agent_name,
                    prompt_template=f"你是{agent_name}代理",
                    full_prompt=f"你是{agent_name}代理，当前日期：{test_date}",
                    prompt_version="1.0.0",
                    source="test"
                )
                
                # 记录代理输出
                output_id = interaction_logger.log_agent_output(
                    agent_name=agent_name,
                    input_id=input_id,
                    prompt_id=prompt_id,
                    raw_response=f"测试响应 - {test_date}",
                    parsed_output={"date": test_date, "result": "success"},
                    processing_time=0.5,
                    confidence=0.9,
                    reasoning=f"基于{test_date}的测试推理"
                )
                
                logger.info(f"    ✅ {agent_name}: 记录完成")
        
        # 验证目录结构
        base_path = Path("test_trading_dates/classification")
        logger.info("\n📊 验证目录结构:")
        
        created_dates = []
        created_agents = set()
        
        if base_path.exists():
            for date_dir in sorted(base_path.iterdir()):
                if date_dir.is_dir():
                    date_name = date_dir.name
                    created_dates.append(date_name)
                    logger.info(f"  📁 {date_name}/")
                    
                    for agent_dir in sorted(date_dir.iterdir()):
                        if agent_dir.is_dir():
                            agent_name = agent_dir.name
                            created_agents.add(agent_name)
                            logger.info(f"    📁 {agent_name}/")
                            
                            # 检查文件
                            files = ["inputs.json", "prompts.json", "outputs.json"]
                            for file_name in files:
                                file_path = agent_dir / file_name
                                if file_path.exists():
                                    file_size = file_path.stat().st_size
                                    logger.info(f"      📄 {file_name} ({file_size} 字节)")
                                else:
                                    logger.error(f"      ❌ {file_name} 缺失")
        
        logger.info(f"\n📈 统计结果:")
        logger.info(f"  创建的日期目录: {len(created_dates)}")
        logger.info(f"  创建的代理目录: {len(created_agents)}")
        logger.info(f"  日期列表: {created_dates}")
        logger.info(f"  代理列表: {sorted(created_agents)}")
        
        return len(created_dates) > 0 and len(created_agents) > 0
        
    except Exception as e:
        logger.error(f"❌ 基于日期的分类测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_agent_integration():
    """测试与代理系统的集成"""
    logger = setup_logger()
    logger.info("🤖 测试代理系统集成")
    
    try:
        from data.agent_interaction_logger import AgentInteractionLogger
        from agents.analyst_agents import NewsAnalystAgent
        
        # 创建日志记录器
        interaction_logger = AgentInteractionLogger(
            base_path="test_trading_dates/integration",
            enabled=True,
            logger=logger,
            use_trading_dates=True
        )
        
        # 创建代理
        agent = NewsAnalystAgent(
            llm_interface=None,  # 不使用真实LLM
            logger=logger,
            interaction_logger=interaction_logger
        )
        
        # 测试不同日期的处理
        test_dates = ["2025-01-15", "2025-02-15", "2025-03-15"]
        
        for test_date in test_dates:
            logger.info(f"  测试日期: {test_date}")
            
            # 创建包含交易日期的状态
            trading_state = {
                "current_date": test_date,
                "stock_data": {"AAPL": {"close": 150.0}},
                "previous_outputs": {}
            }
            
            # 代理处理（会自动记录到对应日期的目录）
            result = agent.call_llm("测试提示词", trading_state)
            
            if result and "agent_id" in result:
                logger.info(f"    ✅ 处理成功: {result['agent_id']}")
            else:
                logger.error(f"    ❌ 处理失败")
        
        # 验证每个日期都有对应的目录
        base_path = Path("test_trading_dates/integration")
        
        for test_date in test_dates:
            date_dir = base_path / test_date / agent.agent_id
            if date_dir.exists():
                logger.info(f"  ✅ {test_date}: 目录创建成功")
            else:
                logger.error(f"  ❌ {test_date}: 目录创建失败")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 代理系统集成测试失败: {e}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    logger = setup_logger()
    
    try:
        import shutil
        
        test_dirs = ["test_trading_dates"]
        
        for test_dir in test_dirs:
            if os.path.exists(test_dir):
                shutil.rmtree(test_dir)
                logger.info(f"🧹 清理测试目录: {test_dir}")
        
    except Exception as e:
        logger.warning(f"⚠️ 清理测试数据失败: {e}")

def main():
    """主测试函数"""
    logger = setup_logger()
    
    logger.info("🧪 测试基于交易日期的分类功能")
    logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 60)
    
    tests = [
        ("交易日期提取", test_trading_date_extraction),
        ("基于日期的分类", test_date_based_classification),
        ("代理系统集成", test_agent_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append(result)
            if result:
                logger.info(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
            results.append(False)
    
    # 总结
    passed = sum(results)
    total = len(results)
    
    logger.info("\n" + "=" * 60)
    logger.info("测试总结")
    logger.info("=" * 60)
    logger.info(f"通过测试: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！基于交易日期的分类功能正常工作。")
        logger.info("\n📖 使用说明:")
        logger.info("现在系统会根据状态数据中的交易日期自动创建目录结构：")
        logger.info("/data/trading/{trading_date}/{agent_name}/")
        logger.info("支持的日期字段: current_date, trading_date, analysis_period.end_date")
    else:
        logger.warning(f"⚠️ {total - passed} 个测试失败")
    
    # 清理测试数据
    cleanup_test_data()
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
