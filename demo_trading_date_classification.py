#!/usr/bin/env python3
"""
演示基于交易日期的分类功能

展示如何使用新的基于交易日期的代理日志记录功能
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_demo_logger():
    """设置演示日志记录器"""
    logger = logging.getLogger("demo_trading_date")
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

def demo_trading_date_classification():
    """演示基于交易日期的分类功能"""
    logger = setup_demo_logger()
    
    logger.info("🎯 演示：基于交易日期的代理日志分类")
    logger.info("=" * 60)
    
    try:
        from data.agent_interaction_logger import AgentInteractionLogger
        from agents.analyst_agents import NewsAnalystAgent, TechnicalAnalystAgent
        
        # 创建启用交易日期分类的日志记录器
        interaction_logger = AgentInteractionLogger(
            base_path="demo_trading_classification/trading",
            enabled=True,
            logger=logger,
            use_trading_dates=True  # 关键：启用基于交易日期的分类
        )
        
        logger.info("✅ 创建代理交互日志记录器（启用交易日期分类）")
        
        # 创建多个代理
        agents = {
            "NAA": NewsAnalystAgent(
                llm_interface=None,  # 演示模式
                logger=logger,
                interaction_logger=interaction_logger
            ),
            "TAA": TechnicalAnalystAgent(
                llm_interface=None,  # 演示模式
                logger=logger,
                interaction_logger=interaction_logger
            )
        }
        
        logger.info(f"✅ 创建代理: {list(agents.keys())}")
        
        # 模拟2025年1月到4月的交易日期
        trading_dates = [
            "2025-01-15",  # 一月中旬
            "2025-02-14",  # 二月情人节
            "2025-03-17",  # 三月圣帕特里克节
            "2025-04-01"   # 四月愚人节
        ]
        
        logger.info(f"📅 模拟交易日期: {trading_dates}")
        
        # 为每个交易日期运行代理分析
        for trading_date in trading_dates:
            logger.info(f"\n📊 处理交易日期: {trading_date}")
            
            # 创建包含交易日期的状态数据
            trading_state = {
                "current_date": trading_date,  # 关键：包含交易日期
                "analysis_period": {
                    "start_date": trading_date,
                    "end_date": trading_date
                },
                "stock_data": {
                    "AAPL": {
                        "open": 148.50,
                        "high": 152.00,
                        "low": 147.80,
                        "close": 150.25,
                        "volume": 2500000
                    },
                    "GOOGL": {
                        "open": 2800.00,
                        "high": 2850.00,
                        "low": 2790.00,
                        "close": 2825.00,
                        "volume": 1200000
                    }
                },
                "portfolio": {"AAPL": 100, "GOOGL": 50},
                "cash": 100000.0,
                "previous_outputs": {}
            }
            
            # 运行每个代理
            for agent_id, agent in agents.items():
                logger.info(f"  🤖 运行代理: {agent_id}")
                
                # 代理处理会自动根据trading_date创建目录
                result = agent.call_llm(
                    agent.get_prompt_template(),
                    trading_state
                )
                
                if result and "agent_id" in result:
                    logger.info(f"    ✅ 处理成功")
                else:
                    logger.error(f"    ❌ 处理失败")
        
        # 显示创建的目录结构
        logger.info(f"\n📁 创建的目录结构:")
        base_path = Path("demo_trading_classification/trading")
        
        if base_path.exists():
            # 按日期排序显示
            for date_dir in sorted(base_path.iterdir()):
                if date_dir.is_dir():
                    logger.info(f"  📅 {date_dir.name}/")
                    
                    # 按代理排序显示
                    for agent_dir in sorted(date_dir.iterdir()):
                        if agent_dir.is_dir():
                            logger.info(f"    🤖 {agent_dir.name}/")
                            
                            # 显示文件
                            total_size = 0
                            file_count = 0
                            for file_path in sorted(agent_dir.iterdir()):
                                if file_path.is_file():
                                    file_size = file_path.stat().st_size
                                    total_size += file_size
                                    file_count += 1
                                    logger.info(f"      📄 {file_path.name} ({file_size} 字节)")
                            
                            logger.info(f"      📊 总计: {file_count} 个文件, {total_size} 字节")
        
        # 获取所有代理的日志摘要
        all_summary = interaction_logger.get_all_agents_summary()
        logger.info(f"\n📈 日志摘要:")
        logger.info(f"  实验日期范围: {min(trading_dates)} 到 {max(trading_dates)}")
        logger.info(f"  总代理数: {all_summary.get('total_agents', 0)}")
        
        for agent_name, agent_summary in all_summary.get("agents", {}).items():
            logger.info(f"  {agent_name}:")
            logger.info(f"    输入记录: {agent_summary.get('inputs_count', 0)}")
            logger.info(f"    提示词记录: {agent_summary.get('prompts_count', 0)}")
            logger.info(f"    输出记录: {agent_summary.get('outputs_count', 0)}")
        
        # 演示导出功能
        logger.info(f"\n📤 演示导出功能:")
        for agent_name in agents.keys():
            export_result = interaction_logger.export_agent_logs(agent_name, "json")
            if export_result.get("success", False):
                logger.info(f"  ✅ {agent_name}: 导出成功 -> {export_result['export_path']}")
            else:
                logger.error(f"  ❌ {agent_name}: 导出失败")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 演示失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def show_usage_examples():
    """显示使用示例"""
    logger = setup_demo_logger()
    
    logger.info("\n📖 使用示例:")
    logger.info("=" * 60)
    
    logger.info("1. 启用基于交易日期的日志记录:")
    logger.info("   python run_opro_system.py --provider zhipuai --enable-agent-logging")
    
    logger.info("\n2. 自定义日志路径:")
    logger.info("   python run_opro_system.py --provider zhipuai \\")
    logger.info("       --enable-agent-logging \\")
    logger.info("       --agent-log-path 'custom_logs/trading'")
    
    logger.info("\n3. 查看日志摘要:")
    logger.info("   python run_opro_system.py --provider zhipuai \\")
    logger.info("       --enable-agent-logging \\")
    logger.info("       --agent-log-summary")
    
    logger.info("\n4. 导出日志数据:")
    logger.info("   python run_opro_system.py --provider zhipuai \\")
    logger.info("       --enable-agent-logging \\")
    logger.info("       --export-agent-logs")
    
    logger.info("\n📁 目录结构说明:")
    logger.info("   /data/trading/")
    logger.info("   ├── 2025-01-15/          # 交易日期目录")
    logger.info("   │   ├── NAA/             # 新闻分析代理")
    logger.info("   │   │   ├── inputs.json  # 输入数据")
    logger.info("   │   │   ├── prompts.json # 提示词数据")
    logger.info("   │   │   └── outputs.json # 输出数据")
    logger.info("   │   └── TAA/             # 技术分析代理")
    logger.info("   │       ├── inputs.json")
    logger.info("   │       ├── prompts.json")
    logger.info("   │       └── outputs.json")
    logger.info("   ├── 2025-02-14/          # 另一个交易日期")
    logger.info("   │   ├── NAA/")
    logger.info("   │   └── TAA/")
    logger.info("   └── ...")
    
    logger.info("\n🔍 支持的日期字段:")
    logger.info("   - current_date: 当前交易日期")
    logger.info("   - trading_date: 明确的交易日期")
    logger.info("   - analysis_period.end_date: 分析期间结束日期")

def cleanup_demo_data():
    """清理演示数据"""
    logger = setup_demo_logger()
    
    try:
        import shutil
        
        demo_dirs = ["demo_trading_classification"]
        
        for demo_dir in demo_dirs:
            if os.path.exists(demo_dir):
                shutil.rmtree(demo_dir)
                logger.info(f"🧹 清理演示目录: {demo_dir}")
        
    except Exception as e:
        logger.warning(f"⚠️ 清理演示数据失败: {e}")

def main():
    """主演示函数"""
    logger = setup_demo_logger()
    
    logger.info("🎬 基于交易日期的代理日志分类功能演示")
    logger.info(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 运行演示
        success = demo_trading_date_classification()
        
        if success:
            logger.info("\n🎉 演示成功完成！")
            
            # 显示使用示例
            show_usage_examples()
            
            logger.info("\n✨ 新功能亮点:")
            logger.info("✅ 自动根据交易日期创建目录结构")
            logger.info("✅ 支持2025-01-01到2025-04-01的日期范围")
            logger.info("✅ 每个交易日期独立存储代理数据")
            logger.info("✅ 便于按日期分析代理性能")
            logger.info("✅ 支持批量导出和分析")
            
        else:
            logger.error("❌ 演示失败")
        
        return success
        
    finally:
        # 清理演示数据
        cleanup_demo_data()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
