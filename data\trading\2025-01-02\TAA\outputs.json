[{"timestamp": "2025-07-04T21:05:11.684141", "output_id": "output_20250704_210511_693690c2", "input_id": "input_20250704_210506_78c02d98", "prompt_id": "prompt_20250704_210507_9b00ae53", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 95, "current_price": 120, "signal": "current price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:11.684141", "processing_time": 5.065665, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 95, "current_price": 120, "signal": "current price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:11.684141", "processing_time": 5.065665, "llm_used": true}, "processing_time": 5.065665, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 12.699942}}, {"timestamp": "2025-07-04T21:05:12.048347", "output_id": "output_20250704_210512_7b751ad2", "input_id": "input_20250704_210506_2ae362e2", "prompt_id": "prompt_20250704_210506_531b1fa7", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 120.0, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 125.0, "200_day_MA": 130.0, "signal": "price above 50-day and 200-day moving averages"}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:12.048347", "processing_time": 5.571179, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 120.0, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 125.0, "200_day_MA": 130.0, "signal": "price above 50-day and 200-day moving averages"}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:12.048347", "processing_time": 5.571179, "llm_used": true}, "processing_time": 5.571179, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 18.271121}}, {"timestamp": "2025-07-04T21:05:12.889285", "output_id": "output_20250704_210512_52375619", "input_id": "input_20250704_210506_935fb464", "prompt_id": "prompt_20250704_210506_6e1c4a90", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "analysis_result": {"trend": "bullish", "support_level": 150.0, "resistance_level": 180.0, "technical_score": 0.75, "indicators": {"RSI": {"current_value": 69, "analysis": "The RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 0.1, "analysis": "The MACD line is above the signal line, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 170.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, supporting a bullish trend."}}, "confidence": 0.9}, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:12.889285", "processing_time": 6.402596, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "analysis_result": {"trend": "bullish", "support_level": 150.0, "resistance_level": 180.0, "technical_score": 0.75, "indicators": {"RSI": {"current_value": 69, "analysis": "The RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 0.1, "analysis": "The MACD line is above the signal line, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 170.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, supporting a bullish trend."}}, "confidence": 0.9}, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:12.889285", "processing_time": 6.402596, "llm_used": true}, "processing_time": 6.402596, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 24.673717}}, {"timestamp": "2025-07-04T21:05:13.166330", "output_id": "output_20250704_210513_78998c89", "input_id": "input_20250704_210506_5c4f4acf", "prompt_id": "prompt_20250704_210507_2a696ce3", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 120.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.2, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 130.0, "200_day_MA": 140.0, "crossover": "none", "signal": "neutral"}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:13.165332", "processing_time": 6.626065, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 120.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.2, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 130.0, "200_day_MA": 140.0, "crossover": "none", "signal": "neutral"}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:13.165332", "processing_time": 6.626065, "llm_used": true}, "processing_time": 6.626065, "llm_used": true, "confidence": 0.85, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 31.299782}}, {"timestamp": "2025-07-04T21:05:13.357316", "output_id": "output_20250704_210513_b76f00b5", "input_id": "input_20250704_210506_44db34f2", "prompt_id": "prompt_20250704_210506_d71b3263", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 180.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 69, "analysis": "The RSI is above 70, indicating that the stock may be overbought and could be due for a pullback."}, "MACD": {"signal_line": 10, "histogram": {"current_value": 0.5, "analysis": "The MACD histogram is positive and rising, suggesting upward momentum."}}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:13.357316", "processing_time": 6.890662, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 180.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 69, "analysis": "The RSI is above 70, indicating that the stock may be overbought and could be due for a pullback."}, "MACD": {"signal_line": 10, "histogram": {"current_value": 0.5, "analysis": "The MACD histogram is positive and rising, suggesting upward momentum."}}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:13.357316", "processing_time": 6.890662, "llm_used": true}, "processing_time": 6.890662, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 38.190444}}, {"timestamp": "2025-07-04T21:05:13.837711", "output_id": "output_20250704_210513_b28efbce", "input_id": "input_20250704_210507_c76e3c9c", "prompt_id": "prompt_20250704_210507_f53749bd", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 123.45, "resistance_level": 135.67, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 57, "analysis": "RSI is in the middle of the range, indicating neither strong buying nor strong selling pressure."}, "MACD": {"signal_line": 123.5, "histogram": -0.01, "analysis": "The MACD histogram is close to zero, suggesting that there is no clear trend direction at the moment."}, "Moving_Averages": {"50-Day_MA": 130.0, "200-Day_MA": 125.0, "analysis": "The stock is currently below the 50-day moving average but above the 200-day moving average, indicating a possible long-term bullish trend but with a short-term neutral stance."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:13.837711", "processing_time": 7.085061, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 123.45, "resistance_level": 135.67, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 57, "analysis": "RSI is in the middle of the range, indicating neither strong buying nor strong selling pressure."}, "MACD": {"signal_line": 123.5, "histogram": -0.01, "analysis": "The MACD histogram is close to zero, suggesting that there is no clear trend direction at the moment."}, "Moving_Averages": {"50-Day_MA": 130.0, "200-Day_MA": 125.0, "analysis": "The stock is currently below the 50-day moving average but above the 200-day moving average, indicating a possible long-term bullish trend but with a short-term neutral stance."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:13.837711", "processing_time": 7.085061, "llm_used": true}, "processing_time": 7.085061, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 45.275504999999995}}, {"timestamp": "2025-07-04T21:05:13.915971", "output_id": "output_20250704_210513_f4effcf4", "input_id": "input_20250704_210506_28f60ff2", "prompt_id": "prompt_20250704_210507_cd3a7b4e", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "support_level": 100.0, "resistance_level": 120.0, "technical_score": 0.75, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating the stock is in an uptrend."}, "MACD": {"signal_line": 100, "histogram": 0.25, "analysis": "MACD signal line is above the zero line with a rising histogram, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 105, "200_day_MA": 110, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:13.915971", "processing_time": 7.36065, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "support_level": 100.0, "resistance_level": 120.0, "technical_score": 0.75, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating the stock is in an uptrend."}, "MACD": {"signal_line": 100, "histogram": 0.25, "analysis": "MACD signal line is above the zero line with a rising histogram, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 105, "200_day_MA": 110, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:13.915971", "processing_time": 7.36065, "llm_used": true}, "processing_time": 7.36065, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 52.636154999999995}}, {"timestamp": "2025-07-04T21:05:14.694185", "output_id": "output_20250704_210514_7fec5234", "input_id": "input_20250704_210507_33740fc8", "prompt_id": "prompt_20250704_210507_34371ceb", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 100, "histogram": 0.5, "interpretation": "The MACD signal line is above the zero line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 110, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:14.694185", "processing_time": 7.973042, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 100, "histogram": 0.5, "interpretation": "The MACD signal line is above the zero line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 110, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:14.694185", "processing_time": 7.973042, "llm_used": true}, "processing_time": 7.973042, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 60.609196999999995}}, {"timestamp": "2025-07-04T21:05:14.997930", "output_id": "output_20250704_210514_5930e19a", "input_id": "input_20250704_210506_bad6e62e", "prompt_id": "prompt_20250704_210507_358f1c52", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 120.0, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought, suggesting a potential pullback"}, "MACD": {"signal_line": 0.02, "histogram": 0.005, "interpretation": "Positive crossover, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 125, "200_day_MA": 100, "interpretation": "Price above 50-day and 200-day MAs, confirming bullish trend"}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:14.997930", "processing_time": 8.359916, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 120.0, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought, suggesting a potential pullback"}, "MACD": {"signal_line": 0.02, "histogram": 0.005, "interpretation": "Positive crossover, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 125, "200_day_MA": 100, "interpretation": "Price above 50-day and 200-day MAs, confirming bullish trend"}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:14.997930", "processing_time": 8.359916, "llm_used": true}, "processing_time": 8.359916, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 68.969113}}, {"timestamp": "2025-07-04T21:05:15.284685", "output_id": "output_20250704_210515_ac5fc074", "input_id": "input_20250704_210506_f5f967bc", "prompt_id": "prompt_20250704_210507_c03f8b66", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 120.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 52, "analysis": "The RSI is neutral, indicating neither strong buying nor selling pressure."}, "MACD": {"signal_line": 0.03, "analysis": "The MACD signal line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 130.0, "200_day_MA": 140.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, which could indicate a period of consolidation."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:15.284685", "processing_time": 8.683232, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 120.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 52, "analysis": "The RSI is neutral, indicating neither strong buying nor selling pressure."}, "MACD": {"signal_line": 0.03, "analysis": "The MACD signal line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 130.0, "200_day_MA": 140.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, which could indicate a period of consolidation."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:15.284685", "processing_time": 8.683232, "llm_used": true}, "processing_time": 8.683232, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 77.652345}}, {"timestamp": "2025-07-04T21:05:16.156453", "output_id": "output_20250704_210516_f4eb292c", "input_id": "input_20250704_210506_a98041d4", "prompt_id": "prompt_20250704_210506_301115e3", "raw_response": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "trend": "bullish", "support_level": {"level1": "100", "level2": "95", "level3": "90"}, "resistance_level": {"level1": "120", "level2": "125", "level3": "130"}, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 69, "trend": "increasing", "interpretation": "indicating potential for further price gains"}, "MACD": {"signal_line": "crossing above zero line", "trend": "positive", "interpretation": "confirming bullish trend"}, "Moving_Average": {"50_day": "moving upwards", "200_day": "also moving upwards", "interpretation": "strong bullish trend supported by long-term and short-term moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:16.156453", "processing_time": 9.698323, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "trend": "bullish", "support_level": {"level1": "100", "level2": "95", "level3": "90"}, "resistance_level": {"level1": "120", "level2": "125", "level3": "130"}, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 69, "trend": "increasing", "interpretation": "indicating potential for further price gains"}, "MACD": {"signal_line": "crossing above zero line", "trend": "positive", "interpretation": "confirming bullish trend"}, "Moving_Average": {"50_day": "moving upwards", "200_day": "also moving upwards", "interpretation": "strong bullish trend supported by long-term and short-term moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:16.156453", "processing_time": 9.698323, "llm_used": true}, "processing_time": 9.698323, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 87.350668}}, {"timestamp": "2025-07-04T21:05:20.440028", "output_id": "output_20250704_210520_ae2fc4f9", "input_id": "input_20250704_210513_8fdfc376", "prompt_id": "prompt_20250704_210513_48354cc3", "raw_response": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "trend": "neutral", "support_level": {"level": "$150.00", "reliability": 0.7}, "resistance_level": {"level": "$175.00", "reliability": 0.8}, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 56, "signal": "neutral"}, "MACD": {"signal": "weak_bullish"}, "moving_averages": {"50_day": "$165.00", "200_day": "$155.00", "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:20.440028", "processing_time": 7.017463, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "trend": "neutral", "support_level": {"level": "$150.00", "reliability": 0.7}, "resistance_level": {"level": "$175.00", "reliability": 0.8}, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 56, "signal": "neutral"}, "MACD": {"signal": "weak_bullish"}, "moving_averages": {"50_day": "$165.00", "200_day": "$155.00", "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:20.440028", "processing_time": 7.017463, "llm_used": true}, "processing_time": 7.017463, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 94.368131}}, {"timestamp": "2025-07-04T21:05:20.747585", "output_id": "output_20250704_210520_dbc6ba0f", "input_id": "input_20250704_210514_29d6f9cf", "prompt_id": "prompt_20250704_210514_ba084db9", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"value": 50.5, "signal": "neutral"}, "MACD": {"signal": "crossing", "hypothesis": "potential trend reversal"}, "Moving_Average": {"50_day_MA": 120.0, "200_day_MA": 130.0, "signal": "crossing"}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:20.747585", "processing_time": 6.652124, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"value": 50.5, "signal": "neutral"}, "MACD": {"signal": "crossing", "hypothesis": "potential trend reversal"}, "Moving_Average": {"50_day_MA": 120.0, "200_day_MA": 130.0, "signal": "crossing"}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:20.747585", "processing_time": 6.652124, "llm_used": true}, "processing_time": 6.652124, "llm_used": true, "confidence": 0.85, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 101.020255}}, {"timestamp": "2025-07-04T21:05:21.069315", "output_id": "output_20250704_210521_95aeed7a", "input_id": "input_20250704_210515_2a53c69c", "prompt_id": "prompt_20250704_210515_28cefe66", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.7, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 100.0, "signal": "bullish crossover"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:21.069315", "processing_time": 5.87263, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.7, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 100.0, "signal": "bullish crossover"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:21.069315", "processing_time": 5.87263, "llm_used": true}, "processing_time": 5.87263, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 106.892885}}, {"timestamp": "2025-07-04T21:05:21.133477", "output_id": "output_20250704_210521_06939815", "input_id": "input_20250704_210513_ebebc84e", "prompt_id": "prompt_20250704_210513_7b69743b", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "bullish", "support_level": {"price": "200", "reason": "Based on the recent market news, support levels are expected to hold due to the positive economic outlook and potential slowdown in inflation."}, "resistance_level": {"price": "250", "reason": "Resistance levels are expected to be tested as market optimism could lead to increased buying pressure and potential price increases."}, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is in the neutral to bullish territory, indicating that the stock may be due for a price increase."}, "MACD": {"signal_line": "Above zero line", "analysis": "The MACD is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": "Above 200-day MA", "analysis": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:21.133477", "processing_time": 7.861421, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "bullish", "support_level": {"price": "200", "reason": "Based on the recent market news, support levels are expected to hold due to the positive economic outlook and potential slowdown in inflation."}, "resistance_level": {"price": "250", "reason": "Resistance levels are expected to be tested as market optimism could lead to increased buying pressure and potential price increases."}, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is in the neutral to bullish territory, indicating that the stock may be due for a price increase."}, "MACD": {"signal_line": "Above zero line", "analysis": "The MACD is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": "Above 200-day MA", "analysis": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:21.133477", "processing_time": 7.861421, "llm_used": true}, "processing_time": 7.861421, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 114.75430600000001}}, {"timestamp": "2025-07-04T21:05:22.885003", "output_id": "output_20250704_210522_88e27a1f", "input_id": "input_20250704_210516_2f03a6e0", "prompt_id": "prompt_20250704_210516_00409e22", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "analysis": "Stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:22.885003", "processing_time": 6.702489, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "analysis": "Stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:22.885003", "processing_time": 6.702489, "llm_used": true}, "processing_time": 6.702489, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 121.45679500000001}}, {"timestamp": "2025-07-04T21:05:25.302833", "output_id": "output_20250704_210525_917b4683", "input_id": "input_20250704_210516_92d55cc4", "prompt_id": "prompt_20250704_210516_f3e19705", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "interpretation": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "neutral"}, "Moving_Average": {"50_day_MA": 125, "200_day_MA": 120, "interpretation": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:25.302833", "processing_time": 8.603387, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "interpretation": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "neutral"}, "Moving_Average": {"50_day_MA": 125, "200_day_MA": 120, "interpretation": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:05:25.302833", "processing_time": 8.603387, "llm_used": true}, "processing_time": 8.603387, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 130.06018200000003}}, {"timestamp": "2025-07-04T21:06:59.922840", "output_id": "output_20250704_210659_74a066f3", "input_id": "input_20250704_210652_dd757655", "prompt_id": "prompt_20250704_210652_113f6623", "raw_response": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "trend": "bearish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": -0.75, "indicators": {"RSI": {"current_value": 30, "interpretation": "oversold", "signal": "bullish"}, "MACD": {"signal_line": -0.05, "histogram": -0.02, "interpretation": "bearish", "signal": "bearish"}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 150.0, "interpretation": "short-term below long-term", "signal": "bearish"}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:06:59.922840", "processing_time": 7.392451, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "trend": "bearish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": -0.75, "indicators": {"RSI": {"current_value": 30, "interpretation": "oversold", "signal": "bullish"}, "MACD": {"signal_line": -0.05, "histogram": -0.02, "interpretation": "bearish", "signal": "bearish"}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 150.0, "interpretation": "short-term below long-term", "signal": "bearish"}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:06:59.922840", "processing_time": 7.392451, "llm_used": true}, "processing_time": 7.392451, "llm_used": true, "confidence": 0.85, "reasoning": "", "metadata": {"analysis_count": 53, "total_processing_time": 377.69729200000006}}, {"timestamp": "2025-07-04T21:07:18.141992", "output_id": "output_20250704_210718_3d71b804", "input_id": "input_20250704_210708_764a1a19", "prompt_id": "prompt_20250704_210708_7bb44667", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 120.0, "resistance_level": 130.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 55, "interpretation": "Indicates a neutral market condition, no overbought or oversold signals."}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "The MACD signal line is close to zero and the histogram is negative, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 125.0, "200_day_MA": 135.0, "interpretation": "The stock is currently trading below its 50-day moving average but above its 200-day moving average, indicating a possible long-term support."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:07:18.141992", "processing_time": 9.976593, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 120.0, "resistance_level": 130.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 55, "interpretation": "Indicates a neutral market condition, no overbought or oversold signals."}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "The MACD signal line is close to zero and the histogram is negative, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 125.0, "200_day_MA": 135.0, "interpretation": "The stock is currently trading below its 50-day moving average but above its 200-day moving average, indicating a possible long-term support."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:07:18.141992", "processing_time": 9.976593, "llm_used": true}, "processing_time": 9.976593, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 61, "total_processing_time": 435.62057400000003}}, {"timestamp": "2025-07-04T21:07:21.338050", "output_id": "output_20250704_210721_e096ed2f", "input_id": "input_20250704_210714_583d189c", "prompt_id": "prompt_20250704_210714_2a14a413", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "bearish crossover"}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 90.0, "interpretation": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:07:21.338050", "processing_time": 6.91162, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "bearish crossover"}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 90.0, "interpretation": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:07:21.338050", "processing_time": 6.91162, "llm_used": true}, "processing_time": 6.91162, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 62, "total_processing_time": 442.53219400000006}}, {"timestamp": "2025-07-04T21:07:35.933314", "output_id": "output_20250704_210735_cac11ef6", "input_id": "input_20250704_210730_8c2fdb39", "prompt_id": "prompt_20250704_210730_a0468a98", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 200.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 50, "analysis": "The RSI is currently neutral, indicating no strong overbought or oversold conditions."}, "MACD": {"signal_line": 100, "histogram": -5, "analysis": "The MACD signal line is near the center, and the histogram is slightly negative, suggesting a neutral trend."}, "Moving_Averages": {"50_day_MA": 150, "200_day_MA": 190, "analysis": "The 50-day moving average is below the 200-day moving average, indicating a potential long-term bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:07:35.933314", "processing_time": 5.890521, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 200.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 50, "analysis": "The RSI is currently neutral, indicating no strong overbought or oversold conditions."}, "MACD": {"signal_line": 100, "histogram": -5, "analysis": "The MACD signal line is near the center, and the histogram is slightly negative, suggesting a neutral trend."}, "Moving_Averages": {"50_day_MA": 150, "200_day_MA": 190, "analysis": "The 50-day moving average is below the 200-day moving average, indicating a potential long-term bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:07:35.933314", "processing_time": 5.890521, "llm_used": true}, "processing_time": 5.890521, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 67, "total_processing_time": 481.314609}}, {"timestamp": "2025-07-04T21:07:40.315266", "output_id": "output_20250704_210740_9386e109", "input_id": "input_20250704_210731_b7e7701a", "prompt_id": "prompt_20250704_210731_491b6cea", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "analysis_result": {"trend": "bullish", "support_level": {"price": 100.0}, "resistance_level": {"price": 150.0}, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought, suggesting a potential pullback or correction."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "The MACD line is above the signal line and the histogram is positive, indicating a bullish trend."}, "Moving_Average": {"50_day_MA": 120.0, "200_day_MA": 110.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:07:40.315266", "processing_time": 8.424936, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "analysis_result": {"trend": "bullish", "support_level": {"price": 100.0}, "resistance_level": {"price": 150.0}, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought, suggesting a potential pullback or correction."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "The MACD line is above the signal line and the histogram is positive, indicating a bullish trend."}, "Moving_Average": {"50_day_MA": 120.0, "200_day_MA": 110.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:07:40.315266", "processing_time": 8.424936, "llm_used": true}, "processing_time": 8.424936, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 72, "total_processing_time": 514.203178}}, {"timestamp": "2025-07-04T21:07:45.475243", "output_id": "output_20250704_210745_d4041859", "input_id": "input_20250704_210739_eae68b2d", "prompt_id": "prompt_20250704_210739_f300b2a7", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 150.0, "resistance_level": 180.0, "technical_score": 0.1, "indicators": {"RSI": {"current_value": 50, "signal": "neutral"}, "MACD": {"signal_line": "crossing below the zero line", "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 170.0, "signal": "neutral"}}, "confidence": 0.7}, "agent_id": "TAA", "timestamp": "2025-07-04T21:07:45.475243", "processing_time": 5.501899, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 150.0, "resistance_level": 180.0, "technical_score": 0.1, "indicators": {"RSI": {"current_value": 50, "signal": "neutral"}, "MACD": {"signal_line": "crossing below the zero line", "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 170.0, "signal": "neutral"}}, "confidence": 0.7}, "agent_id": "TAA", "timestamp": "2025-07-04T21:07:45.475243", "processing_time": 5.501899, "llm_used": true}, "processing_time": 5.501899, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 74, "total_processing_time": 527.25754}}, {"timestamp": "2025-07-04T21:07:46.351534", "output_id": "output_20250704_210746_3e7369ba", "input_id": "input_20250704_210736_54afd397", "prompt_id": "prompt_20250704_210736_0cb64cc1", "raw_response": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 60, "analysis": "The RSI is in the middle of the neutral range, suggesting no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "analysis": "The MACD is close to the zero line, indicating a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 130.0, "analysis": "The stock is currently below its 50-day moving average but above its 200-day moving average, suggesting a potential long-term bullish trend but with short-term consolidation."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:07:46.351534", "processing_time": 10.039507, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 60, "analysis": "The RSI is in the middle of the neutral range, suggesting no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "analysis": "The MACD is close to the zero line, indicating a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 130.0, "analysis": "The stock is currently below its 50-day moving average but above its 200-day moving average, suggesting a potential long-term bullish trend but with short-term consolidation."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:07:46.351534", "processing_time": 10.039507, "llm_used": true}, "processing_time": 10.039507, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 75, "total_processing_time": 537.2970469999999}}, {"timestamp": "2025-07-04T21:07:58.066522", "output_id": "output_20250704_210758_92c5d9fe", "input_id": "input_20250704_210752_15ec13ca", "prompt_id": "prompt_20250704_210752_9691b755", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 50.0, "resistance_level": 75.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 70.5, "analysis": "RSI is above 70, indicating the market may be overbought and could be due for a pullback."}, "MACD": {"signal_line": 30.2, "analysis": "MACD is in positive territory, suggesting a bullish trend. The histogram is rising, reinforcing the bullish signal."}, "Moving_Averages": {"50_day_MA": 65.0, "200_day_MA": 60.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which are strong bullish signals."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:07:58.066522", "processing_time": 5.992738, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 50.0, "resistance_level": 75.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 70.5, "analysis": "RSI is above 70, indicating the market may be overbought and could be due for a pullback."}, "MACD": {"signal_line": 30.2, "analysis": "MACD is in positive territory, suggesting a bullish trend. The histogram is rising, reinforcing the bullish signal."}, "Moving_Averages": {"50_day_MA": 65.0, "200_day_MA": 60.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which are strong bullish signals."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:07:58.066522", "processing_time": 5.992738, "llm_used": true}, "processing_time": 5.992738, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 78, "total_processing_time": 556.797702}}, {"timestamp": "2025-07-04T21:08:01.727767", "output_id": "output_20250704_210801_4075d3f6", "input_id": "input_20250704_210751_056c163b", "prompt_id": "prompt_20250704_210751_1ab353f8", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 120.5, "resistance_level": 130.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 54, "analysis": "The RSI is in the middle of the range, indicating neither strong buying nor selling pressure."}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "analysis": "The MACD signal line is close to zero, suggesting a lack of strong trend momentum."}, "Moving_Averages": {"50_day_MA": 125.0, "200_day_MA": 115.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, indicating a potential lack of direction."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:08:01.727767", "processing_time": 10.22015, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 120.5, "resistance_level": 130.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 54, "analysis": "The RSI is in the middle of the range, indicating neither strong buying nor selling pressure."}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "analysis": "The MACD signal line is close to zero, suggesting a lack of strong trend momentum."}, "Moving_Averages": {"50_day_MA": 125.0, "200_day_MA": 115.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, indicating a potential lack of direction."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:08:01.727767", "processing_time": 10.22015, "llm_used": true}, "processing_time": 10.22015, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 79, "total_processing_time": 567.017852}}, {"timestamp": "2025-07-04T21:08:08.441394", "output_id": "output_20250704_210808_b378f94a", "input_id": "input_20250704_210801_8225ab3c", "prompt_id": "prompt_20250704_210801_7c41aabb", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 50, "interpretation": "Indicates a neutral market condition"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "No clear trend, signal line close to zero suggests no strong direction"}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 130.0, "interpretation": "Stock price is between 50-day and 200-day moving averages, indicating a neutral trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:08:08.441394", "processing_time": 7.224242, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 50, "interpretation": "Indicates a neutral market condition"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "No clear trend, signal line close to zero suggests no strong direction"}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 130.0, "interpretation": "Stock price is between 50-day and 200-day moving averages, indicating a neutral trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:08:08.441394", "processing_time": 7.224242, "llm_used": true}, "processing_time": 7.224242, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 82, "total_processing_time": 586.368226}}, {"timestamp": "2025-07-04T21:08:11.194270", "output_id": "output_20250704_210811_35a763d9", "input_id": "input_20250704_210804_7ce41d06", "prompt_id": "prompt_20250704_210804_12faf6ad", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.8, "indicators": {"RSI": {"value": 68, "analysis": "The RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "analysis": "The MACD line is above the signal line and the histogram is positive, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 180.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:08:11.194270", "processing_time": 7.010627, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.8, "indicators": {"RSI": {"value": 68, "analysis": "The RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "analysis": "The MACD line is above the signal line and the histogram is positive, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 180.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:08:11.194270", "processing_time": 7.010627, "llm_used": true}, "processing_time": 7.010627, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 83, "total_processing_time": 593.378853}}, {"timestamp": "2025-07-04T21:08:15.162846", "output_id": "output_20250704_210815_1ddf2ec0", "input_id": "input_20250704_210809_109b9fd7", "prompt_id": "prompt_20250704_210809_19a555d4", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 50, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "Slight bearish trend"}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 130.0, "interpretation": "Slightly bearish crossover"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:08:15.162846", "processing_time": 5.512891, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 50, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "Slight bearish trend"}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 130.0, "interpretation": "Slightly bearish crossover"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:08:15.162846", "processing_time": 5.512891, "llm_used": true}, "processing_time": 5.512891, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 85, "total_processing_time": 604.4781}}, {"timestamp": "2025-07-04T21:08:20.727527", "output_id": "output_20250704_210820_921af1e5", "input_id": "input_20250704_210812_dc3cdba6", "prompt_id": "prompt_20250704_210812_31240e42", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "The MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 170, "200_day_MA": 180, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:08:20.727527", "processing_time": 8.099911, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "The MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 170, "200_day_MA": 180, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:08:20.727527", "processing_time": 8.099911, "llm_used": true}, "processing_time": 8.099911, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 88, "total_processing_time": 624.139844}}, {"timestamp": "2025-07-04T21:08:24.236173", "output_id": "output_20250704_210824_59699639", "input_id": "input_20250704_210817_4bbba260", "prompt_id": "prompt_20250704_210817_72d374aa", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"signal_line": 0, "histogram": 0, "analysis": "MACD line is close to the zero line, suggesting a lack of strong directional momentum."}, "moving_averages": {"50_day_MA": 120, "200_day_MA": 130, "analysis": "The 50-day moving average is below the 200-day moving average, indicating a potential bearish trend in the long term."}}, "confidence": 0.7}, "agent_id": "TAA", "timestamp": "2025-07-04T21:08:24.236173", "processing_time": 6.848636, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"signal_line": 0, "histogram": 0, "analysis": "MACD line is close to the zero line, suggesting a lack of strong directional momentum."}, "moving_averages": {"50_day_MA": 120, "200_day_MA": 130, "analysis": "The 50-day moving average is below the 200-day moving average, indicating a potential bearish trend in the long term."}}, "confidence": 0.7}, "agent_id": "TAA", "timestamp": "2025-07-04T21:08:24.236173", "processing_time": 6.848636, "llm_used": true}, "processing_time": 6.848636, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 91, "total_processing_time": 641.2614310000001}}, {"timestamp": "2025-07-04T21:08:27.468128", "output_id": "output_20250704_210827_91d2e7ba", "input_id": "input_20250704_210822_1edafab7", "prompt_id": "prompt_20250704_210822_1737c12a", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.75, "indicators": {"RSI": {"value": 70, "comment": "RSI接近超买区域，显示短期内可能有回调风险"}, "MACD": {"signal": "positive", "comment": "MACD线在零轴上方，表明当前趋势是看涨的"}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 90, "comment": "50日均线在200日均线上方，显示长期趋势看涨"}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:08:27.468128", "processing_time": 5.22418, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.75, "indicators": {"RSI": {"value": 70, "comment": "RSI接近超买区域，显示短期内可能有回调风险"}, "MACD": {"signal": "positive", "comment": "MACD线在零轴上方，表明当前趋势是看涨的"}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 90, "comment": "50日均线在200日均线上方，显示长期趋势看涨"}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:08:27.468128", "processing_time": 5.22418, "llm_used": true}, "processing_time": 5.22418, "llm_used": true, "confidence": 0.85, "reasoning": "", "metadata": {"analysis_count": 94, "total_processing_time": 658.0175750000002}}, {"timestamp": "2025-07-04T21:13:37.189604", "output_id": "output_20250704_211337_89ea7b54", "input_id": "input_20250704_211332_6af42327", "prompt_id": "prompt_20250704_211332_21767638", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 54, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.005, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 120, "200_day_MA": 110, "signal": "neutral"}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:37.189604", "processing_time": 5.441315, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 54, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.005, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 120, "200_day_MA": 110, "signal": "neutral"}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:37.189604", "processing_time": 5.441315, "llm_used": true}, "processing_time": 5.441315, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 130, "total_processing_time": 869.186384}}, {"timestamp": "2025-07-04T21:13:38.426934", "output_id": "output_20250704_211338_d722c4ef", "input_id": "input_20250704_211332_263b351a", "prompt_id": "prompt_20250704_211333_6cdb6474", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "bullish", "support_level": 95.5, "resistance_level": 102.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 70, "analysis": "The RSI is above 70, indicating the stock is overbought."}, "MACD": {"signal_line": 100, "histogram": -0.5, "analysis": "The MACD signal line is above the MACD line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 100.0, "200_day_MA": 95.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:38.426934", "processing_time": 6.451466, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "bullish", "support_level": 95.5, "resistance_level": 102.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 70, "analysis": "The RSI is above 70, indicating the stock is overbought."}, "MACD": {"signal_line": 100, "histogram": -0.5, "analysis": "The MACD signal line is above the MACD line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 100.0, "200_day_MA": 95.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:38.426934", "processing_time": 6.451466, "llm_used": true}, "processing_time": 6.451466, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 131, "total_processing_time": 875.63785}}, {"timestamp": "2025-07-04T21:13:38.681382", "output_id": "output_20250704_211338_9b2c0d1f", "input_id": "input_20250704_211331_9bdf99fd", "prompt_id": "prompt_20250704_211332_0eeb1f69", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "indicating an overbought condition but still within a bullish territory"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "MACD line is below the signal line and the histogram is negative, suggesting a bearish trend but a weak one"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "price above the 50-day MA and 200-day MA, which is a bullish sign"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:38.680377", "processing_time": 6.997682, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "indicating an overbought condition but still within a bullish territory"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "MACD line is below the signal line and the histogram is negative, suggesting a bearish trend but a weak one"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "price above the 50-day MA and 200-day MA, which is a bullish sign"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:38.680377", "processing_time": 6.997682, "llm_used": true}, "processing_time": 6.997682, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 132, "total_processing_time": 882.635532}}, {"timestamp": "2025-07-04T21:13:38.932199", "output_id": "output_20250704_211338_03992705", "input_id": "input_20250704_211332_d76537ea", "prompt_id": "prompt_20250704_211332_12345699", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 50.0, "resistance_level": 60.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "neutral"}, "moving_averages": {"50_day_MA": 55.0, "200_day_MA": 52.0, "signal": "neutral"}}, "confidence": 0.7}, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:38.932199", "processing_time": 7.175828, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 50.0, "resistance_level": 60.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "neutral"}, "moving_averages": {"50_day_MA": 55.0, "200_day_MA": 52.0, "signal": "neutral"}}, "confidence": 0.7}, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:38.932199", "processing_time": 7.175828, "llm_used": true}, "processing_time": 7.175828, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 133, "total_processing_time": 889.81136}}, {"timestamp": "2025-07-04T21:13:39.092548", "output_id": "output_20250704_211339_360661bc", "input_id": "input_20250704_211332_f3f2a4a6", "prompt_id": "prompt_20250704_211332_275d7df5", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 150.0, "resistance_level": 180.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "analysis": "The MACD line is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:39.092548", "processing_time": 7.294463, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 150.0, "resistance_level": 180.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "analysis": "The MACD line is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:39.092548", "processing_time": 7.294463, "llm_used": true}, "processing_time": 7.294463, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 134, "total_processing_time": 897.105823}}, {"timestamp": "2025-07-04T21:13:39.152490", "output_id": "output_20250704_211339_2dab0b45", "input_id": "input_20250704_211331_2c8d0523", "prompt_id": "prompt_20250704_211332_e7def2a4", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 110.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "analysis": "The RSI is currently at 50, indicating neither overbought nor oversold conditions. This suggests a neutral trend."}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "analysis": "The MACD signal line is slightly positive, but the histogram is negative, indicating a potential trend reversal. However, it's not a strong signal yet."}, "Moving_Average": {"50_day_MA": 105.0, "200_day_MA": 95.0, "analysis": "The 50-day moving average is above the 200-day moving average, suggesting a long-term bullish trend, but the short-term trend is neutral as it is currently close to the 50-day MA."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:39.139223", "processing_time": 7.490865, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 110.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "analysis": "The RSI is currently at 50, indicating neither overbought nor oversold conditions. This suggests a neutral trend."}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "analysis": "The MACD signal line is slightly positive, but the histogram is negative, indicating a potential trend reversal. However, it's not a strong signal yet."}, "Moving_Average": {"50_day_MA": 105.0, "200_day_MA": 95.0, "analysis": "The 50-day moving average is above the 200-day moving average, suggesting a long-term bullish trend, but the short-term trend is neutral as it is currently close to the 50-day MA."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:39.139223", "processing_time": 7.490865, "llm_used": true}, "processing_time": 7.490865, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 135, "total_processing_time": 904.596688}}, {"timestamp": "2025-07-04T21:13:39.563904", "output_id": "output_20250704_211339_0bc04a36", "input_id": "input_20250704_211332_5cf0980d", "prompt_id": "prompt_20250704_211333_ab420c76", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "neutral", "support_level": "52.50", "resistance_level": "56.00", "technical_score": 0.1, "indicators": {"RSI": {"current_value": 56, "reading": "overbought", "signal": "wait for a pullback"}, "MACD": {"signal_line": 52, "historical_line": 54, "crossing": "historical line crossing above signal line", "signal": "potential bullish trend"}, "Moving_Average": {"50_day_MA": 53.5, "200_day_MA": 51.0, "crossover": "50-day MA crossing above 200-day MA", "signal": "medium-term bullish trend"}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:39.530497", "processing_time": 7.566119, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "neutral", "support_level": "52.50", "resistance_level": "56.00", "technical_score": 0.1, "indicators": {"RSI": {"current_value": 56, "reading": "overbought", "signal": "wait for a pullback"}, "MACD": {"signal_line": 52, "historical_line": 54, "crossing": "historical line crossing above signal line", "signal": "potential bullish trend"}, "Moving_Average": {"50_day_MA": 53.5, "200_day_MA": 51.0, "crossover": "50-day MA crossing above 200-day MA", "signal": "medium-term bullish trend"}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:39.530497", "processing_time": 7.566119, "llm_used": true}, "processing_time": 7.566119, "llm_used": true, "confidence": 0.85, "reasoning": "", "metadata": {"analysis_count": 136, "total_processing_time": 912.1628069999999}}, {"timestamp": "2025-07-04T21:13:39.887735", "output_id": "output_20250704_211339_b2636294", "input_id": "input_20250704_211332_08601af4", "prompt_id": "prompt_20250704_211332_9886b18e", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.2, "interpretation": "Indicates a neutral trend, with neither strong overbought nor oversold conditions."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "Signal line is flat, indicating no strong bullish or bearish trend. Histogram is close to zero, suggesting no strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 170.0, "interpretation": "The stock is currently below its 50-day moving average but above its 200-day moving average, suggesting a possible consolidation phase."}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:39.857804", "processing_time": 8.10244, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.2, "interpretation": "Indicates a neutral trend, with neither strong overbought nor oversold conditions."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "Signal line is flat, indicating no strong bullish or bearish trend. Histogram is close to zero, suggesting no strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 170.0, "interpretation": "The stock is currently below its 50-day moving average but above its 200-day moving average, suggesting a possible consolidation phase."}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:39.857804", "processing_time": 8.10244, "llm_used": true}, "processing_time": 8.10244, "llm_used": true, "confidence": 0.85, "reasoning": "", "metadata": {"analysis_count": 137, "total_processing_time": 920.2652469999999}}, {"timestamp": "2025-07-04T21:13:40.047386", "output_id": "output_20250704_211340_d2250e49", "input_id": "input_20250704_211332_66ae3ec5", "prompt_id": "prompt_20250704_211333_4944ab7b", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "bullish", "support_level": 120.0, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 125.0, "200_day_MA": 130.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, supporting a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:39.964966", "processing_time": 8.166881, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "bullish", "support_level": 120.0, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 125.0, "200_day_MA": 130.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, supporting a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:39.964966", "processing_time": 8.166881, "llm_used": true}, "processing_time": 8.166881, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 138, "total_processing_time": 928.4321279999999}}, {"timestamp": "2025-07-04T21:13:40.085980", "output_id": "output_20250704_211340_36a1dca5", "input_id": "input_20250704_211332_3d6f1732", "prompt_id": "prompt_20250704_211332_5308452e", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "The MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, supporting the bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:40.039276", "processing_time": 8.121573, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "The MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, supporting the bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:40.039276", "processing_time": 8.121573, "llm_used": true}, "processing_time": 8.121573, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 139, "total_processing_time": 936.5537009999999}}, {"timestamp": "2025-07-04T21:13:40.761409", "output_id": "output_20250704_211340_14e90977", "input_id": "input_20250704_211331_af0bebb4", "prompt_id": "prompt_20250704_211332_70dcfc4c", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "analysis_result": {"trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 70, indicating that the stock may be overbought and may be due for a pullback."}, "MACD": {"signal_line": 0.02, "interpretation": "The MACD signal line is above the MACD line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 90.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9}, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:40.761409", "processing_time": 9.073712, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "analysis_result": {"trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 70, indicating that the stock may be overbought and may be due for a pullback."}, "MACD": {"signal_line": 0.02, "interpretation": "The MACD signal line is above the MACD line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 90.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9}, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:40.761409", "processing_time": 9.073712, "llm_used": true}, "processing_time": 9.073712, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 140, "total_processing_time": 945.6274129999999}}, {"timestamp": "2025-07-04T21:13:43.805403", "output_id": "output_20250704_211343_8850370c", "input_id": "input_20250704_211338_4eba7fdb", "prompt_id": "prompt_20250704_211338_4e3862f6", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 50, "signal": "neutral"}, "MACD": {"signal": "crossing", "current_histogram": 0.1}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 90, "signal": "crossing"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:43.805403", "processing_time": 5.761033, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 50, "signal": "neutral"}, "MACD": {"signal": "crossing", "current_histogram": 0.1}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 90, "signal": "crossing"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:43.805403", "processing_time": 5.761033, "llm_used": true}, "processing_time": 5.761033, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 141, "total_processing_time": 951.3884459999999}}, {"timestamp": "2025-07-04T21:13:44.105555", "output_id": "output_20250704_211344_ed3ae726", "input_id": "input_20250704_211339_7186f4dc", "prompt_id": "prompt_20250704_211340_a0322072", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 10.2, "histogram": -5.8, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 120.0, "200_day_MA": 130.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:44.104556", "processing_time": 4.215315, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 10.2, "histogram": -5.8, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 120.0, "200_day_MA": 130.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:44.104556", "processing_time": 4.215315, "llm_used": true}, "processing_time": 4.215315, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 142, "total_processing_time": 955.603761}}, {"timestamp": "2025-07-04T21:13:46.229395", "output_id": "output_20250704_211346_bea549d5", "input_id": "input_20250704_211339_528b8df4", "prompt_id": "prompt_20250704_211339_c8636840", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 55, "interpretation": "slightly above neutral zone"}, "MACD": {"signal_line": 0.01, "histogram": -0.05, "interpretation": "downtrend signal, but not strong"}, "Moving_Averages": {"50_day_MA": 160, "200_day_MA": 190, "interpretation": "short-term MA below long-term MA, suggesting a bearish trend"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:46.215966", "processing_time": 6.398171, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 55, "interpretation": "slightly above neutral zone"}, "MACD": {"signal_line": 0.01, "histogram": -0.05, "interpretation": "downtrend signal, but not strong"}, "Moving_Averages": {"50_day_MA": 160, "200_day_MA": 190, "interpretation": "short-term MA below long-term MA, suggesting a bearish trend"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:46.215966", "processing_time": 6.398171, "llm_used": true}, "processing_time": 6.398171, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 143, "total_processing_time": 962.001932}}, {"timestamp": "2025-07-04T21:13:46.570283", "output_id": "output_20250704_211346_9eb93a17", "input_id": "input_20250704_211340_ac1ce1bf", "prompt_id": "prompt_20250704_211340_c99d03c2", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 90, "signal": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:46.570283", "processing_time": 6.655798, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 90, "signal": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:46.570283", "processing_time": 6.655798, "llm_used": true}, "processing_time": 6.655798, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 144, "total_processing_time": 968.65773}}, {"timestamp": "2025-07-04T21:13:46.623351", "output_id": "output_20250704_211346_56c81850", "input_id": "input_20250704_211339_523089f5", "prompt_id": "prompt_20250704_211339_05da8b7a", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.0, "signal": "neutral"}, "MACD": {"signal": "crossed_zero", "hypothesis": "neutral"}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 130.0, "signal": "crossing_below"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:46.610812", "processing_time": 6.90333, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.0, "signal": "neutral"}, "MACD": {"signal": "crossed_zero", "hypothesis": "neutral"}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 130.0, "signal": "crossing_below"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:46.610812", "processing_time": 6.90333, "llm_used": true}, "processing_time": 6.90333, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 145, "total_processing_time": 975.56106}}, {"timestamp": "2025-07-04T21:13:49.122190", "output_id": "output_20250704_211349_03fa68ae", "input_id": "input_20250704_211343_52e2ef54", "prompt_id": "prompt_20250704_211343_f840d7b7", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 53, "analysis": "RSI is neutral, suggesting no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "analysis": "MACD histogram is positive and crossing above the signal line, indicating a slight bullish trend."}, "Moving_Average": {"50_day_MA": 160, "200_day_MA": 180, "analysis": "The stock is currently below the 50-day MA and 200-day MA, suggesting a potential bearish trend in the long term."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:49.122190", "processing_time": 5.659735, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 53, "analysis": "RSI is neutral, suggesting no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "analysis": "MACD histogram is positive and crossing above the signal line, indicating a slight bullish trend."}, "Moving_Average": {"50_day_MA": 160, "200_day_MA": 180, "analysis": "The stock is currently below the 50-day MA and 200-day MA, suggesting a potential bearish trend in the long term."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:13:49.122190", "processing_time": 5.659735, "llm_used": true}, "processing_time": 5.659735, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 146, "total_processing_time": 981.220795}}, {"timestamp": "2025-07-04T21:15:18.623830", "output_id": "output_20250704_211518_c47f752c", "input_id": "input_20250704_211511_e73ea922", "prompt_id": "prompt_20250704_211511_3d01476f", "raw_response": {"trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 62, "analysis": "The RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"signal_line": 0.03, "histogram": -0.02, "analysis": "The MACD signal line is close to zero and the histogram is negative, suggesting a potential lack of momentum."}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 130.0, "analysis": "The 50-day moving average is below the 200-day moving average, indicating a bearish trend in the long term, but the short-term trend is slightly bullish as it has crossed the 50-day MA."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:15:18.623830", "processing_time": 6.98448, "llm_used": true}, "parsed_output": {"trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 62, "analysis": "The RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"signal_line": 0.03, "histogram": -0.02, "analysis": "The MACD signal line is close to zero and the histogram is negative, suggesting a potential lack of momentum."}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 130.0, "analysis": "The 50-day moving average is below the 200-day moving average, indicating a bearish trend in the long term, but the short-term trend is slightly bullish as it has crossed the 50-day MA."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:15:18.623830", "processing_time": 6.98448, "llm_used": true}, "processing_time": 6.98448, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 179, "total_processing_time": 1203.4936630000007}}, {"timestamp": "2025-07-04T21:15:32.941958", "output_id": "output_20250704_211532_59560206", "input_id": "input_20250704_211526_2e0fd5d1", "prompt_id": "prompt_20250704_211526_9a7c43df", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 54, "signal": "neutral"}, "MACD": {"signal_line": 0.001, "histogram": -0.005, "signal": "neutral"}, "moving_averages": {"50_day_MA": 165.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:15:32.941958", "processing_time": 6.01243, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 54, "signal": "neutral"}, "MACD": {"signal_line": 0.001, "histogram": -0.005, "signal": "neutral"}, "moving_averages": {"50_day_MA": 165.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:15:32.941958", "processing_time": 6.01243, "llm_used": true}, "processing_time": 6.01243, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 187, "total_processing_time": 1253.736034000001}}, {"timestamp": "2025-07-04T21:15:45.175075", "output_id": "output_20250704_211545_a694c987", "input_id": "input_20250704_211538_4f0ba186", "prompt_id": "prompt_20250704_211538_aad1fc3d", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 120.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating a strong upward momentum."}, "MACD": {"signal_line": 10, "histogram": 5, "interpretation": "The MACD line is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 125, "200_day_MA": 130, "interpretation": "The 50-day moving average is above the 200-day moving average, indicating a long-term bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:15:45.175075", "processing_time": 6.220665, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 120.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating a strong upward momentum."}, "MACD": {"signal_line": 10, "histogram": 5, "interpretation": "The MACD line is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 125, "200_day_MA": 130, "interpretation": "The 50-day moving average is above the 200-day moving average, indicating a long-term bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:15:45.175075", "processing_time": 6.220665, "llm_used": true}, "processing_time": 6.220665, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 192, "total_processing_time": 1292.530805000001}}, {"timestamp": "2025-07-04T21:16:01.247077", "output_id": "output_20250704_211601_b1617a36", "input_id": "input_20250704_211553_33b820d8", "prompt_id": "prompt_20250704_211553_dade7e43", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis": {"trend": "bullish", "support_level": 50.0, "resistance_level": 60.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 50, "analysis": "MACD is above the signal line, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 55.0, "200_day_MA": 45.0, "analysis": "The stock is trading above the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:01.247077", "processing_time": 7.720495, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis": {"trend": "bullish", "support_level": 50.0, "resistance_level": 60.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 50, "analysis": "MACD is above the signal line, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 55.0, "200_day_MA": 45.0, "analysis": "The stock is trading above the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:01.247077", "processing_time": 7.720495, "llm_used": true}, "processing_time": 7.720495, "llm_used": true, "confidence": null, "reasoning": {"trend": "bullish", "support_level": 50.0, "resistance_level": 60.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 50, "analysis": "MACD is above the signal line, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 55.0, "200_day_MA": 45.0, "analysis": "The stock is trading above the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95}, "metadata": {"analysis_count": 197, "total_processing_time": 1322.123406000001}}, {"timestamp": "2025-07-04T21:16:02.899723", "output_id": "output_20250704_211602_0bf5f2fc", "input_id": "input_20250704_211558_b3fdbab8", "prompt_id": "prompt_20250704_211558_2bc52a14", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.1, "indicators": {"RSI": {"value": 50, "signal": "neutral"}, "MACD": {"signal_line": "above_zero_line", "hypothesis": "neutral"}, "moving_averages": {"50_day_MA": 120.0, "200_day_MA": 110.0, "signal": "neutral"}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:02.899723", "processing_time": 4.871411, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.1, "indicators": {"RSI": {"value": 50, "signal": "neutral"}, "MACD": {"signal_line": "above_zero_line", "hypothesis": "neutral"}, "moving_averages": {"50_day_MA": 120.0, "200_day_MA": 110.0, "signal": "neutral"}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:02.899723", "processing_time": 4.871411, "llm_used": true}, "processing_time": 4.871411, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 198, "total_processing_time": 1326.9948170000011}}, {"timestamp": "2025-07-04T21:16:10.229450", "output_id": "output_20250704_211610_559fd905", "input_id": "input_20250704_211602_1269b7cf", "prompt_id": "prompt_20250704_211602_7d7855a9", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 95.0, "resistance_level": 105.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "The MACD is above the signal line and the histogram is positive, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 100.0, "200_day_MA": 90.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:10.229450", "processing_time": 7.417049, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 95.0, "resistance_level": 105.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "The MACD is above the signal line and the histogram is positive, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 100.0, "200_day_MA": 90.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:10.229450", "processing_time": 7.417049, "llm_used": true}, "processing_time": 7.417049, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 200, "total_processing_time": 1341.966654000001}}, {"timestamp": "2025-07-04T21:16:17.522559", "output_id": "output_20250704_211617_1b3d5ee9", "input_id": "input_20250704_211613_ab7242ca", "prompt_id": "prompt_20250704_211613_fbfa16dc", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.9, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 120.0, "200-Day_MA": 90.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:17.520556", "processing_time": 4.137483, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.9, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 120.0, "200-Day_MA": 90.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:17.520556", "processing_time": 4.137483, "llm_used": true}, "processing_time": 4.137483, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 204, "total_processing_time": 1366.501675000001}}, {"timestamp": "2025-07-04T21:16:20.943641", "output_id": "output_20250704_211620_d72afd53", "input_id": "input_20250704_211613_3dd463e6", "prompt_id": "prompt_20250704_211613_16f13ed5", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 95.5, "resistance_level": 102.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 54, "trend": "flat", "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.005, "trend": "flat", "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 100.0, "200_day_MA": 95.0, "crossover": "none", "trend": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:20.943641", "processing_time": 7.014218, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 95.5, "resistance_level": 102.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 54, "trend": "flat", "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.005, "trend": "flat", "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 100.0, "200_day_MA": 95.0, "crossover": "none", "trend": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:20.943641", "processing_time": 7.014218, "llm_used": true}, "processing_time": 7.014218, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 205, "total_processing_time": 1373.515893000001}}, {"timestamp": "2025-07-04T21:16:32.071637", "output_id": "output_20250704_211632_318428b3", "input_id": "input_20250704_211628_b92e7a62", "prompt_id": "prompt_20250704_211628_a0842aa3", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 50.0, "resistance_level": 60.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 10, "histogram": 0, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 55, "200_day_MA": 60, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:32.071637", "processing_time": 3.991481, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 50.0, "resistance_level": 60.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 10, "histogram": 0, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 55, "200_day_MA": 60, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:32.071637", "processing_time": 3.991481, "llm_used": true}, "processing_time": 3.991481, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 208, "total_processing_time": 1390.407857000001}}, {"timestamp": "2025-07-04T21:16:33.251436", "output_id": "output_20250704_211633_d03e2ee4", "input_id": "input_20250704_211622_8555e83a", "prompt_id": "prompt_20250704_211622_d11277e3", "raw_response": {"content": "```json\n{\n  \"analysis_date\": \"2025-01-02\",\n  \"trend\": \"bullish\",\n  \"support_level\": 150.00,\n  \"resistance_level\": 200.00,\n  \"technical_score\": 0.8,\n  \"indicators\": {\n    \"RSI\": {\n      \"current_value\": 68,\n      \"signal\": \"overbought\"\n    },\n    \"MACD\": {\n      \"signal\": \"bullish crossover\",\n      \"historical\": \"positive divergence\"\n    },\n    \"moving_averages\": {\n      \"50_day_MA\": 170.00,\n      \"200_day_MA\": 160.00,\n      \"signal\": \"bullish trend\"\n    }\n  },\n  \"confidence\": 0.95\n}\n```\n\n这个JSON对象是基于以下分析生成的：\n- **趋势方向（trend）**：考虑到市场新闻中科技股的强劲表现和全球经济复苏预期，以及苹果公司的新产品发布，预计股票趋势为看涨。\n- **支撑位（support_level）**：根据当前市场情况和股票价格，设定一个较为稳定的支撑位为150.00。\n- **阻力位（resistance_level）**：考虑到市场的整体趋势和价格行为，设定一个阻力位为200.00。\n- **技术评分（technical_score）**：综合RSI、MACD和移动平均线等技术指标，给出了一个较高的技术评分，为0.8。\n- **关键技术指标分析（indicators）**：RSI读数显示市场可能处于超买状态，但MACD显示看涨交叉和正背离，移动平均线显示出上升趋势。\n- **分析信心度（confidence）**：基于市场新闻和技术指标分析，给出了较高的分析信心度，为0.95。", "type": "text_response", "agent_id": "TAA", "timestamp": "2025-07-04T21:16:33.251436", "processing_time": 10.990995, "llm_used": true}, "parsed_output": {"content": "```json\n{\n  \"analysis_date\": \"2025-01-02\",\n  \"trend\": \"bullish\",\n  \"support_level\": 150.00,\n  \"resistance_level\": 200.00,\n  \"technical_score\": 0.8,\n  \"indicators\": {\n    \"RSI\": {\n      \"current_value\": 68,\n      \"signal\": \"overbought\"\n    },\n    \"MACD\": {\n      \"signal\": \"bullish crossover\",\n      \"historical\": \"positive divergence\"\n    },\n    \"moving_averages\": {\n      \"50_day_MA\": 170.00,\n      \"200_day_MA\": 160.00,\n      \"signal\": \"bullish trend\"\n    }\n  },\n  \"confidence\": 0.95\n}\n```\n\n这个JSON对象是基于以下分析生成的：\n- **趋势方向（trend）**：考虑到市场新闻中科技股的强劲表现和全球经济复苏预期，以及苹果公司的新产品发布，预计股票趋势为看涨。\n- **支撑位（support_level）**：根据当前市场情况和股票价格，设定一个较为稳定的支撑位为150.00。\n- **阻力位（resistance_level）**：考虑到市场的整体趋势和价格行为，设定一个阻力位为200.00。\n- **技术评分（technical_score）**：综合RSI、MACD和移动平均线等技术指标，给出了一个较高的技术评分，为0.8。\n- **关键技术指标分析（indicators）**：RSI读数显示市场可能处于超买状态，但MACD显示看涨交叉和正背离，移动平均线显示出上升趋势。\n- **分析信心度（confidence）**：基于市场新闻和技术指标分析，给出了较高的分析信心度，为0.95。", "type": "text_response", "agent_id": "TAA", "timestamp": "2025-07-04T21:16:33.251436", "processing_time": 10.990995, "llm_used": true}, "processing_time": 10.990995, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 209, "total_processing_time": 1401.3988520000012}}, {"timestamp": "2025-07-04T21:16:35.216765", "output_id": "output_20250704_211635_cef2e4f0", "input_id": "input_20250704_211630_ccece84b", "prompt_id": "prompt_20250704_211630_a5b20e4d", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "neutral", "support_level": {"price": "$50.00", "source": "technical_analysis"}, "resistance_level": {"price": "$70.00", "source": "technical_analysis"}, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 58, "trend": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": "$60.00", "200_day_MA": "$65.00", "trend": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:35.216765", "processing_time": 5.101223, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "neutral", "support_level": {"price": "$50.00", "source": "technical_analysis"}, "resistance_level": {"price": "$70.00", "source": "technical_analysis"}, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 58, "trend": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": "$60.00", "200_day_MA": "$65.00", "trend": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:35.216765", "processing_time": 5.101223, "llm_used": true}, "processing_time": 5.101223, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 211, "total_processing_time": 1415.544937000001}}, {"timestamp": "2025-07-04T21:16:45.910470", "output_id": "output_20250704_211645_fd7c32dc", "input_id": "input_20250704_211637_a7d9c1b5", "prompt_id": "prompt_20250704_211637_9ded3076", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 55, "analysis": "Neutral; The RSI is neither overbought nor oversold, indicating a lack of strong momentum in either direction."}, "MACD": {"signal_line": 0.5, "histogram": -0.2, "analysis": "Neutral; The MACD signal line is close to zero, and the histogram is negative, suggesting a lack of clear trend direction."}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 130.0, "analysis": "Neutral; The stock is currently between its 50-day and 200-day moving averages, indicating a lack of a strong trend."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:45.910470", "processing_time": 8.746925, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 55, "analysis": "Neutral; The RSI is neither overbought nor oversold, indicating a lack of strong momentum in either direction."}, "MACD": {"signal_line": 0.5, "histogram": -0.2, "analysis": "Neutral; The MACD signal line is close to zero, and the histogram is negative, suggesting a lack of clear trend direction."}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 130.0, "analysis": "Neutral; The stock is currently between its 50-day and 200-day moving averages, indicating a lack of a strong trend."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:45.910470", "processing_time": 8.746925, "llm_used": true}, "processing_time": 8.746925, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 216, "total_processing_time": 1451.902961000001}}, {"timestamp": "2025-07-04T21:16:51.507254", "output_id": "output_20250704_211651_cb88bc80", "input_id": "input_20250704_211645_7dec5211", "prompt_id": "prompt_20250704_211645_425fb842", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.1, "indicators": {"RSI": {"current_value": 50, "trend": "neutral"}, "MACD": {"signal_line": 20, "histogram": 0, "trend": "neutral"}, "Moving_Average": {"50_day_MA": 120, "200_day_MA": 130, "crossover": "none"}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:51.507254", "processing_time": 6.205549, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.1, "indicators": {"RSI": {"current_value": 50, "trend": "neutral"}, "MACD": {"signal_line": 20, "histogram": 0, "trend": "neutral"}, "Moving_Average": {"50_day_MA": 120, "200_day_MA": 130, "crossover": "none"}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:51.507254", "processing_time": 6.205549, "llm_used": true}, "processing_time": 6.205549, "llm_used": true, "confidence": 0.75, "reasoning": "", "metadata": {"analysis_count": 218, "total_processing_time": 1463.087567000001}}, {"timestamp": "2025-07-04T21:16:52.321731", "output_id": "output_20250704_211652_293f7db1", "input_id": "input_20250704_211648_51e44c44", "prompt_id": "prompt_20250704_211648_bd38f7f4", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 55, "trend": "neutral"}, "MACD": {"signal_line": 100, "histogram": {"bullish": false, "bearish": false}}, "moving_averages": {"50_day_ma": 120, "200_day_ma": 130, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:52.321731", "processing_time": 4.286184, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 55, "trend": "neutral"}, "MACD": {"signal_line": 100, "histogram": {"bullish": false, "bearish": false}}, "moving_averages": {"50_day_ma": 120, "200_day_ma": 130, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:52.321731", "processing_time": 4.286184, "llm_used": true}, "processing_time": 4.286184, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 220, "total_processing_time": 1473.483660000001}}, {"timestamp": "2025-07-04T21:16:55.271489", "output_id": "output_20250704_211655_26f87168", "input_id": "input_20250704_211648_e821400e", "prompt_id": "prompt_20250704_211648_a2c37217", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is neutral, indicating neither strong buying nor selling pressure."}, "MACD": {"signal_line": 100, "histogram": -5, "analysis": "MACD is slightly below the signal line, suggesting a slight bearish bias but not a strong trend."}, "Moving_Average": {"50_day_MA": 120, "200_day_MA": 130, "analysis": "The stock is currently below both the 50-day and 200-day moving averages, indicating a potential downward trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:55.271489", "processing_time": 6.661992, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is neutral, indicating neither strong buying nor selling pressure."}, "MACD": {"signal_line": 100, "histogram": -5, "analysis": "MACD is slightly below the signal line, suggesting a slight bearish bias but not a strong trend."}, "Moving_Average": {"50_day_MA": 120, "200_day_MA": 130, "analysis": "The stock is currently below both the 50-day and 200-day moving averages, indicating a potential downward trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:16:55.271489", "processing_time": 6.661992, "llm_used": true}, "processing_time": 6.661992, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 224, "total_processing_time": 1499.205265000001}}, {"timestamp": "2025-07-04T21:22:53.784479", "output_id": "output_20250704_212253_79ff93af", "input_id": "input_20250704_212247_b6734f60", "prompt_id": "prompt_20250704_212248_8e07b628", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought, suggesting a potential pullback or correction."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "interpretation": "Positive crossover and rising histogram indicate bullish momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:53.784479", "processing_time": 5.939152, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought, suggesting a potential pullback or correction."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "interpretation": "Positive crossover and rising histogram indicate bullish momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:53.784479", "processing_time": 5.939152, "llm_used": true}, "processing_time": 5.939152, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 258, "total_processing_time": 1720.122255000001}}, {"timestamp": "2025-07-04T21:22:54.679787", "output_id": "output_20250704_212254_5c71a090", "input_id": "input_20250704_212248_b54d9400", "prompt_id": "prompt_20250704_212249_68ed2873", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought (above 70), indicating a potential pullback"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "MACD signal line is above the zero line, suggesting a bullish trend"}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 120, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend"}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:54.679787", "processing_time": 6.804067, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought (above 70), indicating a potential pullback"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "MACD signal line is above the zero line, suggesting a bullish trend"}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 120, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend"}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:54.679787", "processing_time": 6.804067, "llm_used": true}, "processing_time": 6.804067, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 259, "total_processing_time": 1726.926322000001}}, {"timestamp": "2025-07-04T21:22:54.741491", "output_id": "output_20250704_212254_ab9d5673", "input_id": "input_20250704_212249_4513691a", "prompt_id": "prompt_20250704_212249_4c933b73", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover", "histogram": "increasing"}, "moving_averages": {"50_day_MA": 120, "200_day_MA": 100, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:54.721508", "processing_time": 6.568557, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover", "histogram": "increasing"}, "moving_averages": {"50_day_MA": 120, "200_day_MA": 100, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:54.721508", "processing_time": 6.568557, "llm_used": true}, "processing_time": 6.568557, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 260, "total_processing_time": 1733.494879000001}}, {"timestamp": "2025-07-04T21:22:54.813555", "output_id": "output_20250704_212254_512f178c", "input_id": "input_20250704_212248_6a0d9a37", "prompt_id": "prompt_20250704_212249_688d4096", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 123.45, "resistance_level": 135.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 130.0, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.85}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:54.788235", "processing_time": 6.841842, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 123.45, "resistance_level": 135.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 130.0, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.85}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:54.788235", "processing_time": 6.841842, "llm_used": true}, "processing_time": 6.841842, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 261, "total_processing_time": 1740.336721000001}}, {"timestamp": "2025-07-04T21:22:54.912735", "output_id": "output_20250704_212254_36047d30", "input_id": "input_20250704_212248_79a203cd", "prompt_id": "prompt_20250704_212249_eb955c34", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 72, "overbought": false, "oversold": false, "signal": "strong buy"}, "MACD": {"signal_line": 0.05, "histogram": {"current_value": 0.03, "trend": "positive"}, "signal": "buy"}, "Moving_Averages": {"50_day_MA": 170.0, "200_day_MA": 180.0, "current_price": 175.0, "trend": "above_50_day_MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:54.912735", "processing_time": 6.847017, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 72, "overbought": false, "oversold": false, "signal": "strong buy"}, "MACD": {"signal_line": 0.05, "histogram": {"current_value": 0.03, "trend": "positive"}, "signal": "buy"}, "Moving_Averages": {"50_day_MA": 170.0, "200_day_MA": 180.0, "current_price": 175.0, "trend": "above_50_day_MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:54.912735", "processing_time": 6.847017, "llm_used": true}, "processing_time": 6.847017, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 262, "total_processing_time": 1747.183738000001}}, {"timestamp": "2025-07-04T21:22:55.784490", "output_id": "output_20250704_212255_3ba38906", "input_id": "input_20250704_212248_61456262", "prompt_id": "prompt_20250704_212249_610f1f90", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought, suggesting a potential pullback"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "Signal line is above the zero line, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "Price is above both the 50-day and 200-day moving averages, suggesting long-term bullish trend"}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:55.784490", "processing_time": 7.90877, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought, suggesting a potential pullback"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "Signal line is above the zero line, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "Price is above both the 50-day and 200-day moving averages, suggesting long-term bullish trend"}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:55.784490", "processing_time": 7.90877, "llm_used": true}, "processing_time": 7.90877, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 263, "total_processing_time": 1755.092508000001}}, {"timestamp": "2025-07-04T21:22:56.667855", "output_id": "output_20250704_212256_6b941390", "input_id": "input_20250704_212248_98ce54fe", "prompt_id": "prompt_20250704_212249_581c1abe", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.5, "resistance_level": 105.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.3, "analysis": "The RSI is neutral, indicating no strong momentum in either direction."}, "MACD": {"signal_line": 0.1, "analysis": "The MACD signal line is close to the center, suggesting a lack of strong momentum."}, "Moving_Average": {"50_day_MA": 102.0, "200_day_MA": 103.5, "analysis": "The stock price is currently between the 50-day and 200-day moving averages, indicating a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:56.667855", "processing_time": 8.729985, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.5, "resistance_level": 105.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.3, "analysis": "The RSI is neutral, indicating no strong momentum in either direction."}, "MACD": {"signal_line": 0.1, "analysis": "The MACD signal line is close to the center, suggesting a lack of strong momentum."}, "Moving_Average": {"50_day_MA": 102.0, "200_day_MA": 103.5, "analysis": "The stock price is currently between the 50-day and 200-day moving averages, indicating a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:56.667855", "processing_time": 8.729985, "llm_used": true}, "processing_time": 8.729985, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 264, "total_processing_time": 1763.822493000001}}, {"timestamp": "2025-07-04T21:22:56.887199", "output_id": "output_20250704_212256_d4a8d1ff", "input_id": "input_20250704_212248_b9282d7f", "prompt_id": "prompt_20250704_212249_6f043025", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50 indicating a bullish trend, but caution as it is approaching the overbought threshold."}, "MACD": {"signal_line": 0.01, "histogram": -0.03, "analysis": "MACD is in a bullish crossover with the signal line, suggesting potential upward momentum."}, "Moving_Averages": {"50_day_MA": 165.0, "200_day_MA": 160.0, "analysis": "The 50-day MA is above the 200-day MA, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:56.887199", "processing_time": 8.921735, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50 indicating a bullish trend, but caution as it is approaching the overbought threshold."}, "MACD": {"signal_line": 0.01, "histogram": -0.03, "analysis": "MACD is in a bullish crossover with the signal line, suggesting potential upward momentum."}, "Moving_Averages": {"50_day_MA": 165.0, "200_day_MA": 160.0, "analysis": "The 50-day MA is above the 200-day MA, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:56.887199", "processing_time": 8.921735, "llm_used": true}, "processing_time": 8.921735, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 265, "total_processing_time": 1772.744228000001}}, {"timestamp": "2025-07-04T21:22:57.253205", "output_id": "output_20250704_212257_9bbe1563", "input_id": "input_20250704_212249_5fc53cd2", "prompt_id": "prompt_20250704_212249_41b891a6", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is in the middle of the overbought territory, suggesting that the stock may be due for a pullback."}, "MACD": {"signal_line": 20, "histogram": 0.5, "interpretation": "The MACD signal line is above the zero line, indicating a bullish trend. The positive histogram suggests continued upward momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:57.253205", "processing_time": 9.04751, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is in the middle of the overbought territory, suggesting that the stock may be due for a pullback."}, "MACD": {"signal_line": 20, "histogram": 0.5, "interpretation": "The MACD signal line is above the zero line, indicating a bullish trend. The positive histogram suggests continued upward momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:57.253205", "processing_time": 9.04751, "llm_used": true}, "processing_time": 9.04751, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 266, "total_processing_time": 1781.791738000001}}, {"timestamp": "2025-07-04T21:22:57.545054", "output_id": "output_20250704_212257_6de76986", "input_id": "input_20250704_212248_c1a2a44a", "prompt_id": "prompt_20250704_212249_cc6e108b", "raw_response": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "analysis_result": {"trend": "neutral", "support_level": {"level_1": 100.0, "level_2": 95.0}, "resistance_level": {"level_1": 110.0, "level_2": 115.0}, "technical_score": 0.1, "indicators": {"RSI": {"current_value": 50.2, "trend": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": -0.5, "trend": "bearish"}, "Moving_Averages": {"50_day_MA": 105.0, "200_day_MA": 110.0, "trend": "bearish"}}, "confidence": 0.85}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:57.545054", "processing_time": 9.694576, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "analysis_result": {"trend": "neutral", "support_level": {"level_1": 100.0, "level_2": 95.0}, "resistance_level": {"level_1": 110.0, "level_2": 115.0}, "technical_score": 0.1, "indicators": {"RSI": {"current_value": 50.2, "trend": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": -0.5, "trend": "bearish"}, "Moving_Averages": {"50_day_MA": 105.0, "200_day_MA": 110.0, "trend": "bearish"}}, "confidence": 0.85}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:57.545054", "processing_time": 9.694576, "llm_used": true}, "processing_time": 9.694576, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 267, "total_processing_time": 1791.4863140000011}}, {"timestamp": "2025-07-04T21:22:58.011172", "output_id": "output_20250704_212258_8adf7578", "input_id": "input_20250704_212249_8c6bbd94", "prompt_id": "prompt_20250704_212249_83c9e1ff", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 123.45, "resistance_level": 135.67, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.1, "histogram": {"current_value": 0.2, "trend": "positive"}, "interpretation": "The MACD is showing a positive trend, suggesting a bullish signal."}, "Moving_Average": {"50_day_MA": 130.0, "200_day_MA": 125.0, "interpretation": "The stock is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:58.011172", "processing_time": 9.809471, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 123.45, "resistance_level": 135.67, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.1, "histogram": {"current_value": 0.2, "trend": "positive"}, "interpretation": "The MACD is showing a positive trend, suggesting a bullish signal."}, "Moving_Average": {"50_day_MA": 130.0, "200_day_MA": 125.0, "interpretation": "The stock is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:22:58.011172", "processing_time": 9.809471, "llm_used": true}, "processing_time": 9.809471, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 268, "total_processing_time": 1801.2957850000012}}, {"timestamp": "2025-07-04T21:23:02.851474", "output_id": "output_20250704_212302_c38bf89e", "input_id": "input_20250704_212255_596bff43", "prompt_id": "prompt_20250704_212255_e648f799", "raw_response": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "trend": "neutral", "support_level": 100.0, "resistance_level": 110.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "interpretation": "RSI is at a neutral level, indicating no strong momentum in either direction."}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "MACD lines are crossing at the zero line, suggesting a possible neutral trend."}, "Moving_Average": {"50_day_MA": 105, "200_day_MA": 115, "interpretation": "The 50-day moving average is close to the 200-day moving average, indicating a potential lack of strong trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:23:02.851474", "processing_time": 7.57474, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "trend": "neutral", "support_level": 100.0, "resistance_level": 110.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "interpretation": "RSI is at a neutral level, indicating no strong momentum in either direction."}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "MACD lines are crossing at the zero line, suggesting a possible neutral trend."}, "Moving_Average": {"50_day_MA": 105, "200_day_MA": 115, "interpretation": "The 50-day moving average is close to the 200-day moving average, indicating a potential lack of strong trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:23:02.851474", "processing_time": 7.57474, "llm_used": true}, "processing_time": 7.57474, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 269, "total_processing_time": 1808.8705250000012}}, {"timestamp": "2025-07-04T21:23:02.925312", "output_id": "output_20250704_212302_b808d920", "input_id": "input_20250704_212255_20810eeb", "prompt_id": "prompt_20250704_212255_180710da", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "signal": "overbought", "analysis": "The RSI is above 70, indicating that the stock may be overbought and could be due for a pullback."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover", "analysis": "The MACD signal line has crossed above the MACD line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 110.0, "signal": "bullish", "analysis": "The 50-day moving average is above the 200-day moving average, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:23:02.897243", "processing_time": 7.473743, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "signal": "overbought", "analysis": "The RSI is above 70, indicating that the stock may be overbought and could be due for a pullback."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover", "analysis": "The MACD signal line has crossed above the MACD line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 110.0, "signal": "bullish", "analysis": "The 50-day moving average is above the 200-day moving average, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:23:02.897243", "processing_time": 7.473743, "llm_used": true}, "processing_time": 7.473743, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 270, "total_processing_time": 1816.3442680000012}}, {"timestamp": "2025-07-04T21:23:04.693698", "output_id": "output_20250704_212304_59aa1248", "input_id": "input_20250704_212256_451a28c2", "prompt_id": "prompt_20250704_212256_a396de99", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "analysis_result": {"trend": "bullish", "support_level": 100.5, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 60, indicating a strong bullish momentum."}, "MACD": {"signal_line": 30, "histogram": {"current_value": 0.5, "analysis": "MACD histogram is positive and rising, suggesting bullish momentum."}}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 130, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:23:04.693698", "processing_time": 8.212116, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "analysis_result": {"trend": "bullish", "support_level": 100.5, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 60, indicating a strong bullish momentum."}, "MACD": {"signal_line": 30, "histogram": {"current_value": 0.5, "analysis": "MACD histogram is positive and rising, suggesting bullish momentum."}}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 130, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:23:04.693698", "processing_time": 8.212116, "llm_used": true}, "processing_time": 8.212116, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 271, "total_processing_time": 1824.5563840000011}}, {"timestamp": "2025-07-04T21:23:05.234431", "output_id": "output_20250704_212305_8f6b971c", "input_id": "input_20250704_212256_68c20b8b", "prompt_id": "prompt_20250704_212257_fdbfe920", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.5, "analysis": "The RSI is neutral, indicating that the stock is neither overbought nor oversold."}, "MACD": {"signal_line": 0.0, "histogram": -0.1, "analysis": "The MACD histogram is negative, suggesting a lack of momentum in either direction."}, "Moving_Averages": {"50_day_MA": 110.0, "200_day_MA": 130.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, indicating a potential bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:23:05.234431", "processing_time": 8.263616, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.5, "analysis": "The RSI is neutral, indicating that the stock is neither overbought nor oversold."}, "MACD": {"signal_line": 0.0, "histogram": -0.1, "analysis": "The MACD histogram is negative, suggesting a lack of momentum in either direction."}, "Moving_Averages": {"50_day_MA": 110.0, "200_day_MA": 130.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, indicating a potential bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:23:05.234431", "processing_time": 8.263616, "llm_used": true}, "processing_time": 8.263616, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 272, "total_processing_time": 1832.820000000001}}, {"timestamp": "2025-07-04T21:23:05.973195", "output_id": "output_20250704_212305_2fef9574", "input_id": "input_20250704_212258_9fa13a85", "prompt_id": "prompt_20250704_212258_167f418d", "raw_response": {"trend": "bullish", "support_level": {"price": 100, "confidence": 0.85}, "resistance_level": {"price": 150, "confidence": 0.9}, "technical_score": 0.65, "indicators": {"RSI": {"current_value": 70, "trend": "overbought", "confidence": 0.95}, "MACD": {"signal_line": 0.01, "histogram": {"current": 0.03, "trend": "positive", "confidence": 0.95}, "trend": "bullish", "confidence": 0.9}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 90, "crossover": "bullish", "confidence": 0.8}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:23:05.973195", "processing_time": 7.632528, "llm_used": true}, "parsed_output": {"trend": "bullish", "support_level": {"price": 100, "confidence": 0.85}, "resistance_level": {"price": 150, "confidence": 0.9}, "technical_score": 0.65, "indicators": {"RSI": {"current_value": 70, "trend": "overbought", "confidence": 0.95}, "MACD": {"signal_line": 0.01, "histogram": {"current": 0.03, "trend": "positive", "confidence": 0.95}, "trend": "bullish", "confidence": 0.9}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 90, "crossover": "bullish", "confidence": 0.8}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:23:05.973195", "processing_time": 7.632528, "llm_used": true}, "processing_time": 7.632528, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 273, "total_processing_time": 1840.4525280000012}}, {"timestamp": "2025-07-04T21:23:06.113801", "output_id": "output_20250704_212306_84d6ca0d", "input_id": "input_20250704_212258_483a720c", "prompt_id": "prompt_20250704_212258_190bee49", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "neutral", "support_level": {"price": "$50.00", "confidence": 0.7}, "resistance_level": {"price": "$60.00", "confidence": 0.8}, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 55, "trend": "neutral", "confidence": 0.9}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "trend": "neutral", "confidence": 0.8}, "Moving_Averages": {"50_day_MA": "$55.00", "200_day_MA": "$58.00", "trend": "neutral", "confidence": 0.85}}, "confidence": 0.6, "agent_id": "TAA", "timestamp": "2025-07-04T21:23:06.112797", "processing_time": 7.31337, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "neutral", "support_level": {"price": "$50.00", "confidence": 0.7}, "resistance_level": {"price": "$60.00", "confidence": 0.8}, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 55, "trend": "neutral", "confidence": 0.9}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "trend": "neutral", "confidence": 0.8}, "Moving_Averages": {"50_day_MA": "$55.00", "200_day_MA": "$58.00", "trend": "neutral", "confidence": 0.85}}, "confidence": 0.6, "agent_id": "TAA", "timestamp": "2025-07-04T21:23:06.112797", "processing_time": 7.31337, "llm_used": true}, "processing_time": 7.31337, "llm_used": true, "confidence": 0.6, "reasoning": "", "metadata": {"analysis_count": 274, "total_processing_time": 1847.7658980000012}}, {"timestamp": "2025-07-04T21:24:43.780710", "output_id": "output_20250704_212443_05982eec", "input_id": "input_20250704_212438_594af299", "prompt_id": "prompt_20250704_212438_847b9fe6", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 55, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "Slightly <PERSON>ish"}, "Moving_Averages": {"50-Day_MA": 120.0, "200-Day_MA": 140.0, "interpretation": "Crossing 50-Day MA downwards indicates a slight bearish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:24:43.780710", "processing_time": 5.467937, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 55, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "Slightly <PERSON>ish"}, "Moving_Averages": {"50-Day_MA": 120.0, "200-Day_MA": 140.0, "interpretation": "Crossing 50-Day MA downwards indicates a slight bearish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:24:43.780710", "processing_time": 5.467937, "llm_used": true}, "processing_time": 5.467937, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 309, "total_processing_time": 2078.0978130000003}}, {"timestamp": "2025-07-04T21:24:54.299425", "output_id": "output_20250704_212454_6bd6b1ec", "input_id": "input_20250704_212447_e0785480", "prompt_id": "prompt_20250704_212447_a94020a6", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "bullish", "support_level": 123.45, "resistance_level": 135.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating that the stock is in a strong uptrend."}, "MACD": {"signal_line": 25, "histogram": 0.2, "interpretation": "The MACD signal line is above the MACD line, and the histogram is positive, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 130.0, "200_day_MA": 125.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:24:54.299425", "processing_time": 6.534699, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "bullish", "support_level": 123.45, "resistance_level": 135.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating that the stock is in a strong uptrend."}, "MACD": {"signal_line": 25, "histogram": 0.2, "interpretation": "The MACD signal line is above the MACD line, and the histogram is positive, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 130.0, "200_day_MA": 125.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:24:54.299425", "processing_time": 6.534699, "llm_used": true}, "processing_time": 6.534699, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 314, "total_processing_time": 2107.0820329999997}}, {"timestamp": "2025-07-04T21:25:09.274115", "output_id": "output_20250704_212509_55a2fa93", "input_id": "input_20250704_212503_769562ac", "prompt_id": "prompt_20250704_212503_a262702e", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 70, "overbought_or_oversold": "overbought", "trend": "upward"}, "MACD": {"signal_line": 20, "histogram": 5, "trend": "positive divergence"}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 120, "crossover": "50_day_MA above 200_day_MA"}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:09.274115", "processing_time": 6.261663, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 70, "overbought_or_oversold": "overbought", "trend": "upward"}, "MACD": {"signal_line": 20, "histogram": 5, "trend": "positive divergence"}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 120, "crossover": "50_day_MA above 200_day_MA"}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:09.274115", "processing_time": 6.261663, "llm_used": true}, "processing_time": 6.261663, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 320, "total_processing_time": 2141.827707}}, {"timestamp": "2025-07-04T21:25:19.831541", "output_id": "output_20250704_212519_89ae6886", "input_id": "input_20250704_212512_e17fdaba", "prompt_id": "prompt_20250704_212512_42dab879", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "analysis": "RSI is near the center of its range, indicating a lack of strong momentum in either direction."}, "MACD": {"signal_line": 10, "histogram": -2, "analysis": "The MACD signal line is below the zero line with a negative histogram, suggesting a bearish trend."}, "Moving_Averages": {"50_day_MA": 180, "200_day_MA": 190, "analysis": "The stock price is below both the 50-day and 200-day moving averages, indicating a bearish trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:19.831541", "processing_time": 7.154833, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "analysis": "RSI is near the center of its range, indicating a lack of strong momentum in either direction."}, "MACD": {"signal_line": 10, "histogram": -2, "analysis": "The MACD signal line is below the zero line with a negative histogram, suggesting a bearish trend."}, "Moving_Averages": {"50_day_MA": 180, "200_day_MA": 190, "analysis": "The stock price is below both the 50-day and 200-day moving averages, indicating a bearish trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:19.831541", "processing_time": 7.154833, "llm_used": true}, "processing_time": 7.154833, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 325, "total_processing_time": 2175.791185}}, {"timestamp": "2025-07-04T21:25:24.553783", "output_id": "output_20250704_212524_43afec56", "input_id": "input_20250704_212519_2294c4f9", "prompt_id": "prompt_20250704_212519_af65e5de", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 50.0, "resistance_level": 60.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal": "crossing_over", "histogram": "downtrend"}, "Moving_Averages": {"50_day_MA": 55.0, "200_day_MA": 65.0, "signal": "crossing_above"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:24.553783", "processing_time": 5.492449, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 50.0, "resistance_level": 60.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal": "crossing_over", "histogram": "downtrend"}, "Moving_Averages": {"50_day_MA": 55.0, "200_day_MA": 65.0, "signal": "crossing_above"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:24.553783", "processing_time": 5.492449, "llm_used": true}, "processing_time": 5.492449, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 327, "total_processing_time": 2186.83691}}, {"timestamp": "2025-07-04T21:25:29.536799", "output_id": "output_20250704_212529_f1aa54c5", "input_id": "input_20250704_212521_897367e2", "prompt_id": "prompt_20250704_212521_d95bffb7", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "neutral", "support_level": 50.0, "resistance_level": 60.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 57, "analysis": "The RSI is slightly above 50, indicating a neutral market condition."}, "MACD": {"signal_line": 58, "histogram": -0.2, "analysis": "The MACD signal line is slightly above the zero line, suggesting a neutral trend with a slight bearish bias."}, "Moving_Average": {"50_day_MA": 55.0, "200_day_MA": 65.0, "analysis": "The 50-day moving average is close to the current price, while the 200-day moving average is above, indicating a long-term bullish trend."}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:29.536799", "processing_time": 8.287197, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "neutral", "support_level": 50.0, "resistance_level": 60.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 57, "analysis": "The RSI is slightly above 50, indicating a neutral market condition."}, "MACD": {"signal_line": 58, "histogram": -0.2, "analysis": "The MACD signal line is slightly above the zero line, suggesting a neutral trend with a slight bearish bias."}, "Moving_Average": {"50_day_MA": 55.0, "200_day_MA": 65.0, "analysis": "The 50-day moving average is close to the current price, while the 200-day moving average is above, indicating a long-term bullish trend."}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:29.536799", "processing_time": 8.287197, "llm_used": true}, "processing_time": 8.287197, "llm_used": true, "confidence": 0.85, "reasoning": "", "metadata": {"analysis_count": 330, "total_processing_time": 2205.581688}}, {"timestamp": "2025-07-04T21:25:36.197526", "output_id": "output_20250704_212536_461accab", "input_id": "input_20250704_212530_7d7fa0b5", "prompt_id": "prompt_20250704_212530_c71b8fa6", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating that the stock is in a bullish trend and may continue to rise."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "The MACD signal line is positive and the histogram is rising, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 110, "200_day_MA": 95, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:36.197526", "processing_time": 5.515202, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating that the stock is in a bullish trend and may continue to rise."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "The MACD signal line is positive and the histogram is rising, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 110, "200_day_MA": 95, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:36.197526", "processing_time": 5.515202, "llm_used": true}, "processing_time": 5.515202, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 333, "total_processing_time": 2222.8647670000005}}, {"timestamp": "2025-07-04T21:25:37.149516", "output_id": "output_20250704_212537_e56ac283", "input_id": "input_20250704_212529_c6c91702", "prompt_id": "prompt_20250704_212529_76bbd682", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 100.5, "resistance_level": 105.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 55, "interpretation": "Neither overbought nor oversold, suggesting a stable market condition."}, "MACD": {"signal_line": 100, "histogram": {"current_value": 0.5, "trend": "flat"}, "interpretation": "Signal line close to zero suggests a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 102.0, "200_day_MA": 110.0, "interpretation": "The stock is currently trading below its 50-day moving average but above its 200-day moving average, indicating a potential long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:37.149516", "processing_time": 7.828947, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 100.5, "resistance_level": 105.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 55, "interpretation": "Neither overbought nor oversold, suggesting a stable market condition."}, "MACD": {"signal_line": 100, "histogram": {"current_value": 0.5, "trend": "flat"}, "interpretation": "Signal line close to zero suggests a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 102.0, "200_day_MA": 110.0, "interpretation": "The stock is currently trading below its 50-day moving average but above its 200-day moving average, indicating a potential long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:37.149516", "processing_time": 7.828947, "llm_used": true}, "processing_time": 7.828947, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 334, "total_processing_time": 2230.6937140000005}}, {"timestamp": "2025-07-04T21:25:41.565410", "output_id": "output_20250704_212541_dffef1ef", "input_id": "input_20250704_212535_2a468ae9", "prompt_id": "prompt_20250704_212535_478a0b18", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 150.0, "resistance_level": 180.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 60, "analysis": "The RSI is in the neutral zone, suggesting no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0, "histogram": 0, "analysis": "The MACD line is crossing the signal line at zero, indicating a neutral trend with no clear bullish or bearish bias."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 170.0, "analysis": "The stock is currently between the 50-day and 200-day moving averages, which are both trending upwards, suggesting a slightly bullish trend but with potential for consolidation."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:41.565410", "processing_time": 5.765169, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 150.0, "resistance_level": 180.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 60, "analysis": "The RSI is in the neutral zone, suggesting no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0, "histogram": 0, "analysis": "The MACD line is crossing the signal line at zero, indicating a neutral trend with no clear bullish or bearish bias."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 170.0, "analysis": "The stock is currently between the 50-day and 200-day moving averages, which are both trending upwards, suggesting a slightly bullish trend but with potential for consolidation."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:41.565410", "processing_time": 5.765169, "llm_used": true}, "processing_time": 5.765169, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 335, "total_processing_time": 2236.4588830000002}}, {"timestamp": "2025-07-04T21:25:44.346150", "output_id": "output_20250704_212544_b9dbe363", "input_id": "input_20250704_212539_7bdc172a", "prompt_id": "prompt_20250704_212540_a79aa6b8", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.1, "indicators": {"RSI": {"current_value": 50, "interpretation": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 120, "interpretation": "crossing"}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:44.338594", "processing_time": 4.391604, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.1, "indicators": {"RSI": {"current_value": 50, "interpretation": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 120, "interpretation": "crossing"}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:44.338594", "processing_time": 4.391604, "llm_used": true}, "processing_time": 4.391604, "llm_used": true, "confidence": 0.75, "reasoning": "", "metadata": {"analysis_count": 336, "total_processing_time": 2240.850487}}, {"timestamp": "2025-07-04T21:25:49.284385", "output_id": "output_20250704_212549_b4d64edc", "input_id": "input_20250704_212540_61743371", "prompt_id": "prompt_20250704_212540_18db3cda", "raw_response": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "analysis_result": {"trend": "neutral", "support_level": {"level1": "100", "level2": "95", "level3": "90"}, "resistance_level": {"level1": "110", "level2": "115", "level3": "120"}, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is near the middle, suggesting no strong bullish or bearish momentum."}, "MACD": {"signal_line": 0.1, "histogram": {"bullish": true, "analysis": "The MACD signal line is slightly above the zero line with a rising histogram, indicating a slight bullish trend."}}, "Moving_Averages": {"SMA_50": {"current_value": "105", "trend": "upward"}, "SMA_200": {"current_value": "98", "trend": "upward"}, "analysis": "Both the 50-day and 200-day moving averages are moving upwards, which suggests a potential bullish trend."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:49.250533", "processing_time": 8.794618, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "analysis_result": {"trend": "neutral", "support_level": {"level1": "100", "level2": "95", "level3": "90"}, "resistance_level": {"level1": "110", "level2": "115", "level3": "120"}, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is near the middle, suggesting no strong bullish or bearish momentum."}, "MACD": {"signal_line": 0.1, "histogram": {"bullish": true, "analysis": "The MACD signal line is slightly above the zero line with a rising histogram, indicating a slight bullish trend."}}, "Moving_Averages": {"SMA_50": {"current_value": "105", "trend": "upward"}, "SMA_200": {"current_value": "98", "trend": "upward"}, "analysis": "Both the 50-day and 200-day moving averages are moving upwards, which suggests a potential bullish trend."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:49.250533", "processing_time": 8.794618, "llm_used": true}, "processing_time": 8.794618, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 337, "total_processing_time": 2249.645105}}, {"timestamp": "2025-07-04T21:25:50.494296", "output_id": "output_20250704_212550_4f3616d6", "input_id": "input_20250704_212544_c177951e", "prompt_id": "prompt_20250704_212544_97fd78bd", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 62, "analysis": "The RSI is above 50, indicating a neutral trend with no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "analysis": "The MACD signal line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 175.0, "200_day_MA": 160.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, indicating a possible downward trend in the long term."}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:50.494296", "processing_time": 5.881772, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 62, "analysis": "The RSI is above 50, indicating a neutral trend with no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "analysis": "The MACD signal line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 175.0, "200_day_MA": 160.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, indicating a possible downward trend in the long term."}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:50.494296", "processing_time": 5.881772, "llm_used": true}, "processing_time": 5.881772, "llm_used": true, "confidence": 0.85, "reasoning": "", "metadata": {"analysis_count": 339, "total_processing_time": 2263.7458380000003}}, {"timestamp": "2025-07-04T21:25:56.475302", "output_id": "output_20250704_212556_7ba026ea", "input_id": "input_20250704_212550_d55968d3", "prompt_id": "prompt_20250704_212550_adf30f60", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 123.45, "resistance_level": 135.67, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "trend": "flat"}, "MACD": {"signal_line": 0, "histogram": "muted", "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 130.0, "200_day_MA": 150.0, "trend": "downward"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:56.475302", "processing_time": 5.766851, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 123.45, "resistance_level": 135.67, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "trend": "flat"}, "MACD": {"signal_line": 0, "histogram": "muted", "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 130.0, "200_day_MA": 150.0, "trend": "downward"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:25:56.475302", "processing_time": 5.766851, "llm_used": true}, "processing_time": 5.766851, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 342, "total_processing_time": 2283.123402}}, {"timestamp": "2025-07-04T21:26:00.652189", "output_id": "output_20250704_212600_65ed2721", "input_id": "input_20250704_212552_9b56ce83", "prompt_id": "prompt_20250704_212552_db5a935a", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "interpretation": "Neutral - indicating no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "Neutral - the MACD line is close to the signal line and histogram is near zero, suggesting a lack of strong momentum."}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 130, "interpretation": "Neutral - the stock is moving within a range between the 50-day and 200-day moving averages, indicating a stable but not particularly strong trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:26:00.652189", "processing_time": 8.494497, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "interpretation": "Neutral - indicating no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "Neutral - the MACD line is close to the signal line and histogram is near zero, suggesting a lack of strong momentum."}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 130, "interpretation": "Neutral - the stock is moving within a range between the 50-day and 200-day moving averages, indicating a stable but not particularly strong trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:26:00.652189", "processing_time": 8.494497, "llm_used": true}, "processing_time": 8.494497, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 345, "total_processing_time": 2305.3001190000004}}, {"timestamp": "2025-07-04T21:26:05.187225", "output_id": "output_20250704_212605_ed1bcf24", "input_id": "input_20250704_212558_856406e4", "prompt_id": "prompt_20250704_212558_8b207d57", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.3, "indicators": {"RSI": {"value": 50, "analysis": "Neutral RSI indicates that the stock is neither overbought nor oversold."}, "MACD": {"signal_line": 0, "analysis": "MACD signal line crossing zero suggests a neutral trend."}, "Moving_Average": {"50_day_MA": 175, "200_day_MA": 180, "analysis": "Stock price is between 50-day and 200-day moving averages, indicating a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:26:05.187225", "processing_time": 6.348586, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "neutral", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.3, "indicators": {"RSI": {"value": 50, "analysis": "Neutral RSI indicates that the stock is neither overbought nor oversold."}, "MACD": {"signal_line": 0, "analysis": "MACD signal line crossing zero suggests a neutral trend."}, "Moving_Average": {"50_day_MA": 175, "200_day_MA": 180, "analysis": "Stock price is between 50-day and 200-day moving averages, indicating a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:26:05.187225", "processing_time": 6.348586, "llm_used": true}, "processing_time": 6.348586, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 347, "total_processing_time": 2318.4138670000007}}, {"timestamp": "2025-07-04T21:32:06.188862", "output_id": "output_20250704_213206_bf2f974d", "input_id": "input_20250704_213200_046ef3b1", "prompt_id": "prompt_20250704_213201_da2afcea", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.5, "resistance_level": 105.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": {"current": 0.03, "trend": "flat"}, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 103.0, "200_day_MA": 97.0, "trend": "upward", "signal": "bullish"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:06.077241", "processing_time": 6.177485, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.5, "resistance_level": 105.0, "technical_score": 0.3, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": {"current": 0.03, "trend": "flat"}, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 103.0, "200_day_MA": 97.0, "trend": "upward", "signal": "bullish"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:06.077241", "processing_time": 6.177485, "llm_used": true}, "processing_time": 6.177485, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 386, "total_processing_time": 2583.7155430000016}}, {"timestamp": "2025-07-04T21:32:06.793790", "output_id": "output_20250704_213206_96f18e5c", "input_id": "input_20250704_213201_af27d5ae", "prompt_id": "prompt_20250704_213202_6d97b13d", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought"}, "MACD": {"signal_line": 0, "histogram": 0.5, "interpretation": "Signal line crossover suggests bullish trend"}, "Moving_Average": {"50_day_MA": 160, "200_day_MA": 180, "interpretation": "Price above 50-day and 200-day moving averages, indicating strong bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:06.793790", "processing_time": 6.439987, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought"}, "MACD": {"signal_line": 0, "histogram": 0.5, "interpretation": "Signal line crossover suggests bullish trend"}, "Moving_Average": {"50_day_MA": 160, "200_day_MA": 180, "interpretation": "Price above 50-day and 200-day moving averages, indicating strong bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:06.793790", "processing_time": 6.439987, "llm_used": true}, "processing_time": 6.439987, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 387, "total_processing_time": 2590.155530000002}}, {"timestamp": "2025-07-04T21:32:07.209526", "output_id": "output_20250704_213207_f62668e7", "input_id": "input_20250704_213200_dea25641", "prompt_id": "prompt_20250704_213201_969e8ba6", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.1, "indicators": {"RSI": {"current_value": 55, "analysis": "The RSI is neutral, indicating no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.0, "analysis": "The MACD is close to zero, suggesting a lack of clear direction."}, "Moving_Averages": {"50-Day_MA": 160.0, "200-Day_MA": 180.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, indicating a consolidation phase."}}, "confidence": 0.7}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:07.209526", "processing_time": 7.128635, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.1, "indicators": {"RSI": {"current_value": 55, "analysis": "The RSI is neutral, indicating no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.0, "analysis": "The MACD is close to zero, suggesting a lack of clear direction."}, "Moving_Averages": {"50-Day_MA": 160.0, "200-Day_MA": 180.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, indicating a consolidation phase."}}, "confidence": 0.7}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:07.209526", "processing_time": 7.128635, "llm_used": true}, "processing_time": 7.128635, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 388, "total_processing_time": 2597.284165000002}}, {"timestamp": "2025-07-04T21:32:07.997740", "output_id": "output_20250704_213207_c4fb31c4", "input_id": "input_20250704_213200_2b4ba925", "prompt_id": "prompt_20250704_213201_31e38075", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 50.0, "resistance_level": 60.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 55.0, "200_day_MA": 45.0, "signal": "bullish trend"}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:07.997740", "processing_time": 7.911854, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 50.0, "resistance_level": 60.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 55.0, "200_day_MA": 45.0, "signal": "bullish trend"}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:07.997740", "processing_time": 7.911854, "llm_used": true}, "processing_time": 7.911854, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 389, "total_processing_time": 2605.196019000002}}, {"timestamp": "2025-07-04T21:32:08.091877", "output_id": "output_20250704_213208_46949854", "input_id": "input_20250704_213200_14e9253a", "prompt_id": "prompt_20250704_213201_7715481e", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "analysis_result": {"trend": "bullish", "support_level": {"price": 150.0}, "resistance_level": {"price": 175.0}, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 69, "analysis": "The RSI is in the overbought territory, suggesting a potential pullback or consolidation."}, "MACD": {"signal_line": 10, "histogram": 0.2, "analysis": "The MACD signal line is above the zero line, indicating a bullish trend. The rising histogram suggests continued upward momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, indicating long-term support and a bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:08.066815", "processing_time": 7.997004, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "analysis_result": {"trend": "bullish", "support_level": {"price": 150.0}, "resistance_level": {"price": 175.0}, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 69, "analysis": "The RSI is in the overbought territory, suggesting a potential pullback or consolidation."}, "MACD": {"signal_line": 10, "histogram": 0.2, "analysis": "The MACD signal line is above the zero line, indicating a bullish trend. The rising histogram suggests continued upward momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, indicating long-term support and a bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:08.066815", "processing_time": 7.997004, "llm_used": true}, "processing_time": 7.997004, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 390, "total_processing_time": 2613.1930230000016}}, {"timestamp": "2025-07-04T21:32:08.115931", "output_id": "output_20250704_213208_e89fe125", "input_id": "input_20250704_213200_0932386f", "prompt_id": "prompt_20250704_213201_c687aa90", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 69, "analysis": "The RSI is above 60, indicating that the stock may be in an overbought condition, but not yet at extreme levels."}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "analysis": "The MACD is slightly positive and the histogram is negative, suggesting a neutral trend with slight bullish potential."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "analysis": "The stock is currently above its 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:08.070666", "processing_time": 8.158833, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 69, "analysis": "The RSI is above 60, indicating that the stock may be in an overbought condition, but not yet at extreme levels."}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "analysis": "The MACD is slightly positive and the histogram is negative, suggesting a neutral trend with slight bullish potential."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "analysis": "The stock is currently above its 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:08.070666", "processing_time": 8.158833, "llm_used": true}, "processing_time": 8.158833, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 391, "total_processing_time": 2621.3518560000016}}, {"timestamp": "2025-07-04T21:32:08.357261", "output_id": "output_20250704_213208_b01a3bc7", "input_id": "input_20250704_213159_43fcf552", "prompt_id": "prompt_20250704_213201_2b127ecf", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "analysis_result": {"trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 70, "analysis": "The RSI is above 70, indicating that the stock may be overbought and potentially due for a pullback."}, "MACD": {"signal_line": 30, "analysis": "The MACD line is above the signal line, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 90, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:08.209619", "processing_time": 8.309863, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "analysis_result": {"trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 70, "analysis": "The RSI is above 70, indicating that the stock may be overbought and potentially due for a pullback."}, "MACD": {"signal_line": 30, "analysis": "The MACD line is above the signal line, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 90, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:08.209619", "processing_time": 8.309863, "llm_used": true}, "processing_time": 8.309863, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 392, "total_processing_time": 2629.6617190000015}}, {"timestamp": "2025-07-04T21:32:08.414892", "output_id": "output_20250704_213208_0600535a", "input_id": "input_20250704_213201_59e38490", "prompt_id": "prompt_20250704_213202_c96bde70", "raw_response": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "analysis_result": {"trend": "bullish", "support_level": 100.5, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 70, "comment": "RSI is above 70, indicating the stock may be overbought."}, "MACD": {"signal_line": 10, "histogram": 0.2, "comment": "MACD signal line is above the zero line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 100, "comment": "The stock is trading above both the 50-day and 200-day moving averages, indicating long-term support."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:08.248331", "processing_time": 8.124974, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": "$1,000,000.00", "analysis_result": {"trend": "bullish", "support_level": 100.5, "resistance_level": 150.0, "technical_score": 0.85, "indicators": {"RSI": {"current_value": 70, "comment": "RSI is above 70, indicating the stock may be overbought."}, "MACD": {"signal_line": 10, "histogram": 0.2, "comment": "MACD signal line is above the zero line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 100, "comment": "The stock is trading above both the 50-day and 200-day moving averages, indicating long-term support."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:08.248331", "processing_time": 8.124974, "llm_used": true}, "processing_time": 8.124974, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 393, "total_processing_time": 2637.7866930000014}}, {"timestamp": "2025-07-04T21:32:08.448020", "output_id": "output_20250704_213208_eec3ad66", "input_id": "input_20250704_213200_1b3ac2d7", "prompt_id": "prompt_20250704_213201_8541a306", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 100.5, "resistance_level": 105.0, "technical_score": 0.85, "indicators": {"RSI": {"value": 68, "interpretation": "The RSI is above 50, indicating that the stock is in a bullish trend."}, "MACD": {"signal_line": 104.2, "histogram": 0.1, "interpretation": "The MACD line is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 103.0, "200_day_MA": 102.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:08.321666", "processing_time": 8.366073, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "bullish", "support_level": 100.5, "resistance_level": 105.0, "technical_score": 0.85, "indicators": {"RSI": {"value": 68, "interpretation": "The RSI is above 50, indicating that the stock is in a bullish trend."}, "MACD": {"signal_line": 104.2, "histogram": 0.1, "interpretation": "The MACD line is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 103.0, "200_day_MA": 102.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:08.321666", "processing_time": 8.366073, "llm_used": true}, "processing_time": 8.366073, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 394, "total_processing_time": 2646.1527660000015}}, {"timestamp": "2025-07-04T21:32:08.690848", "output_id": "output_20250704_213208_38d249da", "input_id": "input_20250704_213201_87e7db93", "prompt_id": "prompt_20250704_213202_7f3d679d", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 55, "interpretation": "Neutral - No strong overbought or oversold conditions."}, "MACD": {"signal_line": 50, "histogram": 0, "interpretation": "Neutral - Signal line near center, no clear bullish or bearish trend."}, "Moving_Averages": {"50_day_MA": 160, "200_day_MA": 170, "interpretation": "Neutral - Price near 50-day MA, slightly below 200-day MA, suggesting a stable but slightly bearish trend."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:08.404829", "processing_time": 8.121097, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 55, "interpretation": "Neutral - No strong overbought or oversold conditions."}, "MACD": {"signal_line": 50, "histogram": 0, "interpretation": "Neutral - Signal line near center, no clear bullish or bearish trend."}, "Moving_Averages": {"50_day_MA": 160, "200_day_MA": 170, "interpretation": "Neutral - Price near 50-day MA, slightly below 200-day MA, suggesting a stable but slightly bearish trend."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:08.404829", "processing_time": 8.121097, "llm_used": true}, "processing_time": 8.121097, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 395, "total_processing_time": 2654.2738630000017}}, {"timestamp": "2025-07-04T21:32:09.313524", "output_id": "output_20250704_213209_d117a17b", "input_id": "input_20250704_213201_35cfac86", "prompt_id": "prompt_20250704_213202_84b8c64e", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 123.45, "resistance_level": 125.67, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.2, "analysis": "The RSI is currently neutral, indicating no clear overbought or oversold conditions."}, "MACD": {"signal_line": 0.1, "analysis": "The MACD signal line is slightly above the zero line, suggesting a slight bullish trend but not strong enough to confirm a bullish trend."}, "Moving_Averages": {"50_day_MA": 124.0, "200_day_MA": 120.0, "analysis": "The stock is currently trading above its 50-day moving average but below its 200-day moving average, suggesting a short-term bullish trend but a long-term neutral trend."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:09.313524", "processing_time": 9.074681, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 123.45, "resistance_level": 125.67, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.2, "analysis": "The RSI is currently neutral, indicating no clear overbought or oversold conditions."}, "MACD": {"signal_line": 0.1, "analysis": "The MACD signal line is slightly above the zero line, suggesting a slight bullish trend but not strong enough to confirm a bullish trend."}, "Moving_Averages": {"50_day_MA": 124.0, "200_day_MA": 120.0, "analysis": "The stock is currently trading above its 50-day moving average but below its 200-day moving average, suggesting a short-term bullish trend but a long-term neutral trend."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:09.313524", "processing_time": 9.074681, "llm_used": true}, "processing_time": 9.074681, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 396, "total_processing_time": 2663.3485440000018}}, {"timestamp": "2025-07-04T21:32:11.858862", "output_id": "output_20250704_213211_6ec1c60d", "input_id": "input_20250704_213206_e4d94f7b", "prompt_id": "prompt_20250704_213206_49d7e766", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.75, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": " bullish crossover"}, "Moving_Averages": {"50_day_MA": 125, "200_day_MA": 115, "interpretation": "bullish crossover"}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:11.858862", "processing_time": 5.474651, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.75, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": " bullish crossover"}, "Moving_Averages": {"50_day_MA": 125, "200_day_MA": 115, "interpretation": "bullish crossover"}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:11.858862", "processing_time": 5.474651, "llm_used": true}, "processing_time": 5.474651, "llm_used": true, "confidence": 0.85, "reasoning": "", "metadata": {"analysis_count": 397, "total_processing_time": 2668.8231950000018}}, {"timestamp": "2025-07-04T21:32:12.168138", "output_id": "output_20250704_213212_f5a9e419", "input_id": "input_20250704_213207_96d5b3cb", "prompt_id": "prompt_20250704_213207_57b2c47b", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 200.0, "technical_score": 0.1, "indicators": {"RSI": {"current_value": 50.0, "analysis": "Neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "analysis": "No clear trend"}, "Moving_Average": {"short_term": 150.0, "long_term": 180.0, "current_price": 160.0, "analysis": "Price is near the long-term moving average, indicating potential sideways movement"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:12.168138", "processing_time": 4.479366, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 200.0, "technical_score": 0.1, "indicators": {"RSI": {"current_value": 50.0, "analysis": "Neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "analysis": "No clear trend"}, "Moving_Average": {"short_term": 150.0, "long_term": 180.0, "current_price": 160.0, "analysis": "Price is near the long-term moving average, indicating potential sideways movement"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:12.168138", "processing_time": 4.479366, "llm_used": true}, "processing_time": 4.479366, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 398, "total_processing_time": 2673.302561000002}}, {"timestamp": "2025-07-04T21:32:14.301213", "output_id": "output_20250704_213214_7648c480", "input_id": "input_20250704_213208_43bab0a9", "prompt_id": "prompt_20250704_213209_18a8935f", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 52, "analysis": "Neutral; The RSI is neither overbought nor oversold, indicating a stable trend."}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "analysis": "Neutral; The MACD signal line and histogram are close to zero, suggesting a lack of strong trend."}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 110.0, "analysis": "Neutral; The stock is trading above the 50-day MA but below the 200-day MA, indicating a slightly bullish short-term trend but a neutral to bearish long-term trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:14.301213", "processing_time": 5.891386, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 52, "analysis": "Neutral; The RSI is neither overbought nor oversold, indicating a stable trend."}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "analysis": "Neutral; The MACD signal line and histogram are close to zero, suggesting a lack of strong trend."}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 110.0, "analysis": "Neutral; The stock is trading above the 50-day MA but below the 200-day MA, indicating a slightly bullish short-term trend but a neutral to bearish long-term trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:14.301213", "processing_time": 5.891386, "llm_used": true}, "processing_time": 5.891386, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 399, "total_processing_time": 2679.1939470000016}}, {"timestamp": "2025-07-04T21:32:14.402195", "output_id": "output_20250704_213214_c07b5fd3", "input_id": "input_20250704_213208_2f6d1816", "prompt_id": "prompt_20250704_213208_b10b60d7", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50 but below 70, indicating a neutral market condition with no overbought or oversold signals."}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "interpretation": "The MACD signal line is slightly positive and the histogram is negative, suggesting a slight bullish trend but with cautious optimism."}, "Moving_Average": {"50_day_MA": 110, "200_day_MA": 130, "interpretation": "The stock is currently below its 50-day moving average but above its 200-day moving average, indicating a potential long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:14.325913", "processing_time": 5.968652, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50 but below 70, indicating a neutral market condition with no overbought or oversold signals."}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "interpretation": "The MACD signal line is slightly positive and the histogram is negative, suggesting a slight bullish trend but with cautious optimism."}, "Moving_Average": {"50_day_MA": 110, "200_day_MA": 130, "interpretation": "The stock is currently below its 50-day moving average but above its 200-day moving average, indicating a potential long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:14.325913", "processing_time": 5.968652, "llm_used": true}, "processing_time": 5.968652, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 400, "total_processing_time": 2685.1625990000016}}, {"timestamp": "2025-07-04T21:32:14.941994", "output_id": "output_20250704_213214_85e4c266", "input_id": "input_20250704_213210_12e5060a", "prompt_id": "prompt_20250704_213210_b78d2ae6", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis": {"trend": "neutral", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "interpretation": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "neutral"}, "Moving_Average": {"50_day_MA": 160, "200_day_MA": 190, "interpretation": "crossed_at_50_day_MA"}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:14.871646", "processing_time": 4.29584, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis": {"trend": "neutral", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "interpretation": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "neutral"}, "Moving_Average": {"50_day_MA": 160, "200_day_MA": 190, "interpretation": "crossed_at_50_day_MA"}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:14.871646", "processing_time": 4.29584, "llm_used": true}, "processing_time": 4.29584, "llm_used": true, "confidence": null, "reasoning": {"trend": "neutral", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "interpretation": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "neutral"}, "Moving_Average": {"50_day_MA": 160, "200_day_MA": 190, "interpretation": "crossed_at_50_day_MA"}}, "confidence": 0.8}, "metadata": {"analysis_count": 401, "total_processing_time": 2689.458439000002}}, {"timestamp": "2025-07-04T21:32:18.042373", "output_id": "output_20250704_213218_a1e3871f", "input_id": "input_20250704_213213_4a3d110d", "prompt_id": "prompt_20250704_213213_e8c70519", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.3, "interpretation": "bullish crossover"}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "50-day MA above 200-day MA, indicating a long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:18.040358", "processing_time": 4.476321, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.3, "interpretation": "bullish crossover"}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "50-day MA above 200-day MA, indicating a long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:32:18.040358", "processing_time": 4.476321, "llm_used": true}, "processing_time": 4.476321, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 402, "total_processing_time": 2693.934760000002}}, {"timestamp": "2025-07-04T21:33:39.061301", "output_id": "output_20250704_213339_59095ef4", "input_id": "input_20250704_213333_581c413d", "prompt_id": "prompt_20250704_213334_6ed35f32", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis": {"trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "analysis": "Neutral trend, indicating no overbought or oversold conditions."}, "MACD": {"signal_line": 0, "histogram": 0, "analysis": "MACD is close to zero line, suggesting a lack of strong directional momentum."}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 120, "analysis": "50-day MA is below the 200-day MA, suggesting a potential bearish trend, but not strongly so."}}, "confidence": 0.7}, "agent_id": "TAA", "timestamp": "2025-07-04T21:33:39.061301", "processing_time": 5.107183, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis": {"trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "analysis": "Neutral trend, indicating no overbought or oversold conditions."}, "MACD": {"signal_line": 0, "histogram": 0, "analysis": "MACD is close to zero line, suggesting a lack of strong directional momentum."}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 120, "analysis": "50-day MA is below the 200-day MA, suggesting a potential bearish trend, but not strongly so."}}, "confidence": 0.7}, "agent_id": "TAA", "timestamp": "2025-07-04T21:33:39.061301", "processing_time": 5.107183, "llm_used": true}, "processing_time": 5.107183, "llm_used": true, "confidence": null, "reasoning": {"trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "analysis": "Neutral trend, indicating no overbought or oversold conditions."}, "MACD": {"signal_line": 0, "histogram": 0, "analysis": "MACD is close to zero line, suggesting a lack of strong directional momentum."}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 120, "analysis": "50-day MA is below the 200-day MA, suggesting a potential bearish trend, but not strongly so."}}, "confidence": 0.7}, "metadata": {"analysis_count": 439, "total_processing_time": 2900.6621000000014}}, {"timestamp": "2025-07-04T21:33:54.825792", "output_id": "output_20250704_213354_f52a4147", "input_id": "input_20250704_213349_071a9bbd", "prompt_id": "prompt_20250704_213349_fbffdf61", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "bullish", "support_level": 123.45, "resistance_level": 135.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 130.0, "200_day_MA": 120.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:33:54.825792", "processing_time": 5.365267, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "trend": "bullish", "support_level": 123.45, "resistance_level": 135.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 130.0, "200_day_MA": 120.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:33:54.825792", "processing_time": 5.365267, "llm_used": true}, "processing_time": 5.365267, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 445, "total_processing_time": 2933.650937000002}}, {"timestamp": "2025-07-04T21:33:56.430972", "output_id": "output_20250704_213356_f8991766", "input_id": "input_20250704_213349_521d21e5", "prompt_id": "prompt_20250704_213349_dfb06f2c", "raw_response": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.5, "analysis": "RSI is close to the middle line, indicating neither strong buying nor selling pressure."}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "analysis": "The MACD signal line is slightly above the zero line, suggesting a slight bullish trend, but the histogram is negative, indicating some bearish momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 180.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, suggesting a possible downward trend."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:33:56.374385", "processing_time": 6.847715, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "cash_available": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50.5, "analysis": "RSI is close to the middle line, indicating neither strong buying nor selling pressure."}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "analysis": "The MACD signal line is slightly above the zero line, suggesting a slight bullish trend, but the histogram is negative, indicating some bearish momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 180.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, suggesting a possible downward trend."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:33:56.374385", "processing_time": 6.847715, "llm_used": true}, "processing_time": 6.847715, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 446, "total_processing_time": 2940.498652000002}}, {"timestamp": "2025-07-04T21:34:07.124051", "output_id": "output_20250704_213407_aac586e9", "input_id": "input_20250704_213401_2740b35e", "prompt_id": "prompt_20250704_213401_d3d3a92c", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 123.45, "resistance_level": 135.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought, indicating a possible pullback"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "MACD histogram is rising, suggesting bullish momentum"}, "moving_averages": {"50_day_MA": 130.0, "200_day_MA": 120.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:07.124051", "processing_time": 5.878902, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 123.45, "resistance_level": 135.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought, indicating a possible pullback"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "MACD histogram is rising, suggesting bullish momentum"}, "moving_averages": {"50_day_MA": 130.0, "200_day_MA": 120.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:07.124051", "processing_time": 5.878902, "llm_used": true}, "processing_time": 5.878902, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 450, "total_processing_time": 2963.105262000002}}, {"timestamp": "2025-07-04T21:34:11.654317", "output_id": "output_20250704_213411_3f5f0399", "input_id": "input_20250704_213405_5e404b70", "prompt_id": "prompt_20250704_213405_1943db8d", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 120.0, "resistance_level": 130.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 60, "interpretation": "The RSI is above 50, indicating a neutral market condition."}, "MACD": {"signal_line": 20, "histogram": 0.5, "interpretation": "The MACD signal line is near the center, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 125.0, "200_day_MA": 135.0, "interpretation": "The stock is currently trading below its 50-day moving average but above its 200-day moving average, indicating a short-term bearish trend but a long-term bullish trend."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:11.654317", "processing_time": 6.388017, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "analysis_result": {"trend": "neutral", "support_level": 120.0, "resistance_level": 130.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 60, "interpretation": "The RSI is above 50, indicating a neutral market condition."}, "MACD": {"signal_line": 20, "histogram": 0.5, "interpretation": "The MACD signal line is near the center, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 125.0, "200_day_MA": 135.0, "interpretation": "The stock is currently trading below its 50-day moving average but above its 200-day moving average, indicating a short-term bearish trend but a long-term bullish trend."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:11.654317", "processing_time": 6.388017, "llm_used": true}, "processing_time": 6.388017, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 452, "total_processing_time": 2980.546590000002}}, {"timestamp": "2025-07-04T21:34:16.571847", "output_id": "output_20250704_213416_93f7ab11", "input_id": "input_20250704_213408_42c6723e", "prompt_id": "prompt_20250704_213408_0c73c5ac", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:16.571847", "processing_time": 8.280264, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "bullish", "support_level": 150.0, "resistance_level": 175.0, "technical_score": 0.8, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:16.571847", "processing_time": 8.280264, "llm_used": true}, "processing_time": 8.280264, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 455, "total_processing_time": 2999.2925300000024}}, {"timestamp": "2025-07-04T21:34:18.989770", "output_id": "output_20250704_213418_65057555", "input_id": "input_20250704_213413_92674f3c", "prompt_id": "prompt_20250704_213413_837bd19f", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 54, "analysis": "Neutral - The RSI is neither overbought nor oversold, indicating a stable market."}, "MACD": {"signal_line": 0.01, "histogram": -0.05, "analysis": "Neutral - The MACD signal line is close to zero and the histogram is flat, suggesting no strong trend."}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 120, "analysis": "Slightly bullish - The 50-day moving average is above the 200-day moving average, indicating a possible uptrend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:18.989770", "processing_time": 5.745013, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 54, "analysis": "Neutral - The RSI is neither overbought nor oversold, indicating a stable market."}, "MACD": {"signal_line": 0.01, "histogram": -0.05, "analysis": "Neutral - The MACD signal line is close to zero and the histogram is flat, suggesting no strong trend."}, "Moving_Averages": {"50_day_MA": 110, "200_day_MA": 120, "analysis": "Slightly bullish - The 50-day moving average is above the 200-day moving average, indicating a possible uptrend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:18.989770", "processing_time": 5.745013, "llm_used": true}, "processing_time": 5.745013, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 458, "total_processing_time": 3015.8918540000027}}, {"timestamp": "2025-07-04T21:34:26.453701", "output_id": "output_20250704_213426_2593ab42", "input_id": "input_20250704_213420_1e56f557", "prompt_id": "prompt_20250704_213420_037c23be", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "analysis_result": {"trend": "neutral", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "interpretation": "Neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "No clear trend"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 190.0, "interpretation": "Stock is moving within the middle of its 50 and 200-day moving averages, indicating a neutral trend."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:26.453701", "processing_time": 6.171229, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "analysis_result": {"trend": "neutral", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "interpretation": "Neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "No clear trend"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 190.0, "interpretation": "Stock is moving within the middle of its 50 and 200-day moving averages, indicating a neutral trend."}}, "confidence": 0.8}, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:26.453701", "processing_time": 6.171229, "llm_used": true}, "processing_time": 6.171229, "llm_used": true, "confidence": null, "reasoning": "", "metadata": {"analysis_count": 461, "total_processing_time": 3039.872722000003}}, {"timestamp": "2025-07-04T21:34:33.746640", "output_id": "output_20250704_213433_180f6aaa", "input_id": "input_20250704_213426_78e588c1", "prompt_id": "prompt_20250704_213426_72907402", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is in a neutral zone, suggesting no strong bullish or bearish momentum."}, "MACD": {"signal_line": 0, "histogram": 0, "analysis": "MACD lines are crossing in the center, indicating a lack of strong trend direction."}, "Moving_Averages": {"50-Day_MA": 160.0, "200-Day_MA": 180.0, "analysis": "The stock is currently between the 50-Day and 200-Day moving averages, suggesting a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:33.746640", "processing_time": 7.612456, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 150.0, "resistance_level": 200.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is in a neutral zone, suggesting no strong bullish or bearish momentum."}, "MACD": {"signal_line": 0, "histogram": 0, "analysis": "MACD lines are crossing in the center, indicating a lack of strong trend direction."}, "Moving_Averages": {"50-Day_MA": 160.0, "200-Day_MA": 180.0, "analysis": "The stock is currently between the 50-Day and 200-Day moving averages, suggesting a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:33.746640", "processing_time": 7.612456, "llm_used": true}, "processing_time": 7.612456, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 465, "total_processing_time": 3068.4792300000026}}, {"timestamp": "2025-07-04T21:34:38.769530", "output_id": "output_20250704_213438_f193e38a", "input_id": "input_20250704_213432_52ae078d", "prompt_id": "prompt_20250704_213432_1014c915", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"value": 50, "signal": "neutral"}, "MACD": {"signal": "crossed_zero", "hypothesis": "neutral"}, "Moving_Averages": {"50-Day_MA": 110.0, "200-Day_MA": 130.0, "signal": "crossing"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:38.769530", "processing_time": 5.979835, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"value": 50, "signal": "neutral"}, "MACD": {"signal": "crossed_zero", "hypothesis": "neutral"}, "Moving_Averages": {"50-Day_MA": 110.0, "200-Day_MA": 130.0, "signal": "crossing"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:38.769530", "processing_time": 5.979835, "llm_used": true}, "processing_time": 5.979835, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 466, "total_processing_time": 3074.4590650000027}}, {"timestamp": "2025-07-04T21:34:41.506696", "output_id": "output_20250704_213441_38326a07", "input_id": "input_20250704_213436_89b04cb0", "prompt_id": "prompt_20250704_213436_8c63160c", "raw_response": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 55, "interpretation": "neutral - neither overbought nor oversold"}, "MACD": {"signal_line": 10, "historical_line": 12, "interpretation": "crossing signal line upwards - bullish trend"}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 100, "interpretation": "50-day MA above 200-day MA - potential bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:41.487911", "processing_time": 5.103796, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.5, "indicators": {"RSI": {"current_value": 55, "interpretation": "neutral - neither overbought nor oversold"}, "MACD": {"signal_line": 10, "historical_line": 12, "interpretation": "crossing signal line upwards - bullish trend"}, "Moving_Averages": {"50_day_MA": 120, "200_day_MA": 100, "interpretation": "50-day MA above 200-day MA - potential bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:41.487911", "processing_time": 5.103796, "llm_used": true}, "processing_time": 5.103796, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 467, "total_processing_time": 3079.5628610000026}}, {"timestamp": "2025-07-04T21:34:48.020444", "output_id": "output_20250704_213448_6d750653", "input_id": "input_20250704_213443_93f1d51f", "prompt_id": "prompt_20250704_213443_107be371", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 150.0, "resistance_level": 180.0, "technical_score": 0.2, "indicators": {"RSI": {"value": 50.5, "signal": "neutral"}, "MACD": {"signal": "crossed_zero", "hypothesis": "neutral"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 170.0, "signal": "crossing_below"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:48.020444", "processing_time": 4.96816, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 150.0, "resistance_level": 180.0, "technical_score": 0.2, "indicators": {"RSI": {"value": 50.5, "signal": "neutral"}, "MACD": {"signal": "crossed_zero", "hypothesis": "neutral"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 170.0, "signal": "crossing_below"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:48.020444", "processing_time": 4.96816, "llm_used": true}, "processing_time": 4.96816, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 470, "total_processing_time": 3096.8747720000024}}, {"timestamp": "2025-07-04T21:34:52.914758", "output_id": "output_20250704_213452_91e61099", "input_id": "input_20250704_213446_96d6dd60", "prompt_id": "prompt_20250704_213446_b2cc90b6", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "bullish", "support_level": {"level": 100.0, "confidence": 0.85}, "resistance_level": {"level": 150.0, "confidence": 0.9}, "technical_score": 0.75, "indicators": {"RSI": {"current_value": 68, "signal": "overbought", "confidence": 0.95}, "MACD": {"signal": "bullish crossover", "confidence": 0.85}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 110.0, "signal": "price above 50-day and 200-day MAs", "confidence": 0.8}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:52.914758", "processing_time": 6.891565, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "bullish", "support_level": {"level": 100.0, "confidence": 0.85}, "resistance_level": {"level": 150.0, "confidence": 0.9}, "technical_score": 0.75, "indicators": {"RSI": {"current_value": 68, "signal": "overbought", "confidence": 0.95}, "MACD": {"signal": "bullish crossover", "confidence": 0.85}, "Moving_Averages": {"50_day_MA": 120.0, "200_day_MA": 110.0, "signal": "price above 50-day and 200-day MAs", "confidence": 0.8}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-04T21:34:52.914758", "processing_time": 6.891565, "llm_used": true}, "processing_time": 6.891565, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 473, "total_processing_time": 3122.8272990000023}}, {"timestamp": "2025-07-04T21:35:06.626736", "output_id": "output_20250704_213506_b89b6552", "input_id": "input_20250704_213459_00c6237f", "prompt_id": "prompt_20250704_213459_fffa7e12", "raw_response": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.1, "indicators": {"RSI": {"value": 50, "interpretation": "indicating a neutral trend with no overbought or oversold conditions"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "signal line at zero indicates no clear bullish or bearish trend"}, "Moving_Average": {"short_term_MA": 105.0, "long_term_MA": 125.0, "interpretation": "price is currently below the long-term moving average but above the short-term moving average, suggesting a potential for a slight bullish trend"}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-04T21:35:06.626736", "processing_time": 7.101133, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": "$1,000,000.00", "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.1, "indicators": {"RSI": {"value": 50, "interpretation": "indicating a neutral trend with no overbought or oversold conditions"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "signal line at zero indicates no clear bullish or bearish trend"}, "Moving_Average": {"short_term_MA": 105.0, "long_term_MA": 125.0, "interpretation": "price is currently below the long-term moving average but above the short-term moving average, suggesting a potential for a slight bullish trend"}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-04T21:35:06.626736", "processing_time": 7.101133, "llm_used": true}, "processing_time": 7.101133, "llm_used": true, "confidence": 0.75, "reasoning": "", "metadata": {"analysis_count": 477, "total_processing_time": 3148.9295420000026}}, {"timestamp": "2025-07-04T21:35:08.291728", "output_id": "output_20250704_213508_a11a1d31", "input_id": "input_20250704_213502_77426fdd", "prompt_id": "prompt_20250704_213502_7af3fbc0", "raw_response": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "signal": "neutral"}, "MACD": {"signal": "weak bullish crossover", "histogram": "positive and rising"}, "Moving_Average": {"50_day_MA": 120, "200_day_MA": 100, "signal": "crossing upwards"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:35:08.291728", "processing_time": 5.433608, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "available_cash": 1000000.0, "trend": "neutral", "support_level": 100.0, "resistance_level": 150.0, "technical_score": 0.2, "indicators": {"RSI": {"current_value": 50, "signal": "neutral"}, "MACD": {"signal": "weak bullish crossover", "histogram": "positive and rising"}, "Moving_Average": {"50_day_MA": 120, "200_day_MA": 100, "signal": "crossing upwards"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-04T21:35:08.291728", "processing_time": 5.433608, "llm_used": true}, "processing_time": 5.433608, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 479, "total_processing_time": 3161.4314850000023}}]