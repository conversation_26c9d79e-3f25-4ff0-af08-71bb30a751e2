#!/usr/bin/env python3
"""
最终测试：基于交易日期的分类功能

验证修改后的系统能够正确根据交易日期创建目录结构
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logger():
    """设置日志记录器"""
    logger = logging.getLogger("final_test")
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

def test_complete_workflow():
    """测试完整的工作流程"""
    logger = setup_logger()
    logger.info("🧪 测试完整的基于交易日期的分类工作流程")
    
    try:
        from data.agent_interaction_logger import AgentInteractionLogger
        from agents.analyst_agents import NewsAnalystAgent
        
        # 创建日志记录器
        interaction_logger = AgentInteractionLogger(
            base_path="final_test_logs/trading",
            enabled=True,
            logger=logger,
            use_trading_dates=True
        )
        
        # 创建代理
        agent = NewsAnalystAgent(
            llm_interface=None,  # 不使用真实LLM
            logger=logger,
            interaction_logger=interaction_logger
        )
        
        logger.info(f"创建代理: {agent.agent_id}")
        
        # 测试不同的交易日期
        test_scenarios = [
            {
                "date": "2025-01-15",
                "description": "一月中旬"
            },
            {
                "date": "2025-02-28",
                "description": "二月底"
            },
            {
                "date": "2025-03-31",
                "description": "三月底"
            }
        ]
        
        for scenario in test_scenarios:
            test_date = scenario["date"]
            description = scenario["description"]
            
            logger.info(f"测试场景: {description} ({test_date})")
            
            # 创建包含交易日期的状态数据
            trading_state = {
                "current_date": test_date,
                "analysis_period": {
                    "start_date": test_date,
                    "end_date": test_date
                },
                "stock_data": {
                    "AAPL": {
                        "open": 148.50,
                        "high": 152.00,
                        "low": 147.80,
                        "close": 150.25,
                        "volume": 2500000
                    }
                },
                "portfolio": {"AAPL": 100},
                "cash": 50000.0,
                "previous_outputs": {}
            }
            
            # 运行代理处理
            result = agent.call_llm(agent.get_prompt_template(), trading_state)
            
            if result and "agent_id" in result:
                logger.info(f"  ✅ 代理处理成功: {result['agent_id']}")
            else:
                logger.error(f"  ❌ 代理处理失败")
                continue
            
            # 验证目录结构
            expected_dir = Path("final_test_logs/trading") / test_date / agent.agent_id
            if expected_dir.exists():
                logger.info(f"  ✅ 目录创建成功: {expected_dir}")
                
                # 检查文件
                files = ["inputs.json", "prompts.json", "outputs.json"]
                all_files_exist = True
                
                for file_name in files:
                    file_path = expected_dir / file_name
                    if file_path.exists():
                        file_size = file_path.stat().st_size
                        logger.info(f"    📄 {file_name}: {file_size} 字节")
                    else:
                        logger.error(f"    ❌ {file_name}: 文件缺失")
                        all_files_exist = False
                
                if all_files_exist:
                    logger.info(f"  ✅ 所有文件创建成功")
                else:
                    logger.error(f"  ❌ 部分文件缺失")
            else:
                logger.error(f"  ❌ 目录创建失败: {expected_dir}")
        
        # 显示最终的目录结构
        logger.info("\n📁 最终目录结构:")
        base_path = Path("final_test_logs/trading")
        
        if base_path.exists():
            for date_dir in sorted(base_path.iterdir()):
                if date_dir.is_dir():
                    logger.info(f"  📁 {date_dir.name}/")
                    
                    for agent_dir in sorted(date_dir.iterdir()):
                        if agent_dir.is_dir():
                            logger.info(f"    📁 {agent_dir.name}/")
                            
                            for file_path in sorted(agent_dir.iterdir()):
                                if file_path.is_file():
                                    file_size = file_path.stat().st_size
                                    logger.info(f"      📄 {file_path.name} ({file_size} 字节)")
        
        # 获取日志摘要
        summary = interaction_logger.get_agent_log_summary(agent.agent_id)
        logger.info(f"\n📊 日志摘要:")
        logger.info(f"  代理: {agent.agent_id}")
        logger.info(f"  输入记录: {summary.get('inputs_count', 0)}")
        logger.info(f"  提示词记录: {summary.get('prompts_count', 0)}")
        logger.info(f"  输出记录: {summary.get('outputs_count', 0)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 完整工作流程测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_date_extraction_scenarios():
    """测试各种日期提取场景"""
    logger = setup_logger()
    logger.info("🔍 测试日期提取场景")
    
    try:
        from data.agent_interaction_logger import AgentInteractionLogger
        
        # 创建日志记录器
        interaction_logger = AgentInteractionLogger(
            base_path="final_test_logs/extraction",
            enabled=True,
            logger=logger,
            use_trading_dates=True
        )
        
        # 测试各种日期格式和场景
        test_cases = [
            {
                "name": "标准current_date",
                "state": {"current_date": "2025-01-01"},
                "expected": "2025-01-01"
            },
            {
                "name": "analysis_period结束日期",
                "state": {
                    "analysis_period": {
                        "start_date": "2025-01-01",
                        "end_date": "2025-01-31"
                    }
                },
                "expected": "2025-01-31"
            },
            {
                "name": "trading_date字段",
                "state": {"trading_date": "2025-02-15"},
                "expected": "2025-02-15"
            },
            {
                "name": "复杂状态数据",
                "state": {
                    "current_date": "2025-03-15",
                    "stock_data": {"AAPL": {"price": 150.0}},
                    "portfolio": {"AAPL": 100},
                    "cash": 50000.0
                },
                "expected": "2025-03-15"
            }
        ]
        
        for test_case in test_cases:
            logger.info(f"  测试: {test_case['name']}")
            
            extracted_date = interaction_logger._extract_trading_date(test_case["state"])
            
            if extracted_date == test_case["expected"]:
                logger.info(f"    ✅ 提取成功: {extracted_date}")
            else:
                logger.error(f"    ❌ 提取失败: 期望 {test_case['expected']}, 得到 {extracted_date}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 日期提取测试失败: {e}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    logger = setup_logger()
    
    try:
        import shutil
        
        test_dirs = ["final_test_logs"]
        
        for test_dir in test_dirs:
            if os.path.exists(test_dir):
                shutil.rmtree(test_dir)
                logger.info(f"🧹 清理测试目录: {test_dir}")
        
    except Exception as e:
        logger.warning(f"⚠️ 清理测试数据失败: {e}")

def main():
    """主测试函数"""
    logger = setup_logger()
    
    logger.info("🎯 最终测试：基于交易日期的分类功能")
    logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 60)
    
    tests = [
        ("日期提取场景", test_date_extraction_scenarios),
        ("完整工作流程", test_complete_workflow)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append(result)
            if result:
                logger.info(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
            results.append(False)
    
    # 总结
    passed = sum(results)
    total = len(results)
    
    logger.info("\n" + "=" * 60)
    logger.info("最终测试总结")
    logger.info("=" * 60)
    logger.info(f"通过测试: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！基于交易日期的分类功能已完全实现。")
        logger.info("\n📖 功能说明:")
        logger.info("✅ 系统现在会根据状态数据中的交易日期自动创建目录结构")
        logger.info("✅ 支持的日期字段: current_date, trading_date, analysis_period.end_date")
        logger.info("✅ 目录结构: /data/trading/{trading_date}/{agent_name}/")
        logger.info("✅ 每个代理目录包含: inputs.json, prompts.json, outputs.json")
        logger.info("\n🚀 现在可以运行:")
        logger.info("python run_opro_system.py --provider zhipuai --enable-agent-logging")
    else:
        logger.warning(f"⚠️ {total - passed} 个测试失败，需要进一步调试")
    
    # 清理测试数据
    cleanup_test_data()
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
